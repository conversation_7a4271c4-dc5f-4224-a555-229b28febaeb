pluginManagement {
    repositories {
        google {
            content {
                includeGroupByRegex("com\\.android.*")
                includeGroupByRegex("com\\.google.*")
                includeGroupByRegex("androidx.*")
            }
        }
        maven {
            url = uri("https://git.dmobin.studio/api/v4/groups/11/-/packages/maven")
            name = "DmobinGit"
            credentials(HttpHeaderCredentials::class) {
                name = "Private-Token"
                value = "ivikbRMuJ3C_pDhQ_xnc"
            }
            isAllowInsecureProtocol = true
            authentication {
                create<HttpHeaderAuthentication>("header")
            }
        }
        mavenCentral()
        gradlePluginPortal()
    }
}
dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.FAIL_ON_PROJECT_REPOS)
    repositories {
        google()
        mavenCentral()
        maven {
            url = uri("https://git.dmobin.studio/api/v4/groups/11/-/packages/maven")
            name = "DmobinGit"
            credentials(HttpHeaderCredentials::class) {
                name = "Private-Token"
                value = "ivikbRMuJ3C_pDhQ_xnc"
            }
            isAllowInsecureProtocol = true
            authentication {
                create<HttpHeaderAuthentication>("header")
            }
        }
    }
}

rootProject.name = "Test App"
include(":app")
