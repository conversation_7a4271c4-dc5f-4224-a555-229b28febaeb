package com.truonganim.testapp

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import dev.chrisbanes.haze.HazeState
import dev.chrisbanes.haze.hazeChild
import dev.chrisbanes.haze.rememberHazeState
import com.truonganim.testapp.weather.ui.theme.AppTheme
import com.truonganim.testapp.weather.ui.theme.ThemeManager

class BlurTestActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        setContent {
            val context = LocalContext.current
            val darkTheme = ThemeManager.shouldUseDarkTheme(context)
            
            AppTheme(darkTheme = darkTheme) {
                Scaffold(modifier = Modifier.fillMaxSize()) { innerPadding ->
                    BlurTestScreen(
                        modifier = Modifier.padding(innerPadding)
                    )
                }
            }
        }
    }
}

@Composable
fun BlurTestScreen(
    modifier: Modifier = Modifier
) {
    val hazeState = rememberHazeState()

    // Sample data for cards
    val cardData = remember {
        listOf(
            CardData("Welcome", "Chào mừng đến với blur test", Color(0xFF6366F1)),
            CardData("Glass Effect", "Hiệu ứng kính mờ thực tế", Color(0xFF8B5CF6)),
            CardData("Backdrop Blur", "Blur background real-time", Color(0xFF06B6D4)),
            CardData("Modern UI", "Giao diện hiện đại", Color(0xFF10B981)),
            CardData("Smooth Scroll", "Cuộn mượt mà", Color(0xFFF59E0B)),
            CardData("Dynamic Blur", "Blur động theo vị trí", Color(0xFFEF4444)),
            CardData("Performance", "Hiệu suất tối ưu", Color(0xFF8B5CF6)),
            CardData("Beautiful", "Đẹp mắt và tinh tế", Color(0xFF06B6D4)),
            CardData("Interactive", "Tương tác mượt mà", Color(0xFF10B981)),
            CardData("Amazing", "Tuyệt vời phải không?", Color(0xFFF59E0B)),
            CardData("Scrollable", "Có thể cuộn được", Color(0xFFEF4444)),
            CardData("Real-time", "Blur thời gian thực", Color(0xFF6366F1))
        )
    }

    Box(
        modifier = modifier.fillMaxSize()
    ) {
        // Background image with haze source
        Image(
            painter = painterResource(id = R.drawable.bg),
            contentDescription = "Background Image",
            modifier = Modifier
                .fillMaxSize()
                .hazeChild(hazeState),
            contentScale = ContentScale.Crop
        )

        // Scrollable list of glass morphism cards
        LazyColumn(
            modifier = Modifier.fillMaxSize(),
            contentPadding = PaddingValues(24.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            items(cardData) { card ->
                GlassMorphismCard(
                    title = card.title,
                    subtitle = card.subtitle,
                    accentColor = card.color,
                    hazeState = hazeState
                )
            }
        }
    }
}

data class CardData(
    val title: String,
    val subtitle: String,
    val color: Color
)

@Composable
fun GlassMorphismCard(
    title: String,
    subtitle: String,
    accentColor: Color,
    hazeState: HazeState,
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier
            .fillMaxWidth()
            .height(120.dp)
            .clip(RoundedCornerShape(20.dp))
            .hazeChild(hazeState)
            .background(
                Brush.verticalGradient(
                    colors = listOf(
                        Color.White.copy(alpha = 0.25f),
                        Color.White.copy(alpha = 0.15f)
                    )
                )
            )
            .padding(20.dp),
        contentAlignment = Alignment.Center
    ) {
        // Accent color indicator
        Box(
            modifier = Modifier
                .fillMaxSize()
                .clip(RoundedCornerShape(16.dp))
                .background(
                    Brush.horizontalGradient(
                        colors = listOf(
                            accentColor.copy(alpha = 0.1f),
                            Color.Transparent
                        )
                    )
                )
        )

        // Content layer - clear and sharp
        Column(
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = title,
                fontSize = 20.sp,
                fontWeight = FontWeight.Bold,
                color = Color.White,
                textAlign = TextAlign.Center
            )
            Spacer(modifier = Modifier.height(6.dp))
            Text(
                text = subtitle,
                fontSize = 14.sp,
                color = Color.White.copy(alpha = 0.9f),
                textAlign = TextAlign.Center
            )
        }
    }
}

@Preview(showBackground = true)
@Composable
fun BlurTestScreenPreview() {
    AppTheme {
        BlurTestScreen()
    }
}
