package com.truonganim.testapp

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.blur
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.zIndex
import com.truonganim.testapp.weather.ui.theme.AppTheme
import com.truonganim.testapp.weather.ui.theme.ThemeManager
import dev.chrisbanes.haze.HazeState
import dev.chrisbanes.haze.hazeEffect
import dev.chrisbanes.haze.hazeSource
import dev.chrisbanes.haze.rememberHazeState

class BlurTestActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        setContent {
            val context = LocalContext.current
            val darkTheme = ThemeManager.shouldUseDarkTheme(context)
            
            AppTheme(darkTheme = darkTheme) {
                Scaffold(modifier = Modifier.fillMaxSize()) { innerPadding ->
                    BlurTestScreen(
                        modifier = Modifier.padding(innerPadding)
                    )
                }
            }
        }
    }
}

@Composable
fun BlurTestScreen(
    modifier: Modifier = Modifier
) {
    val hazeState = rememberHazeState()
    Box(
        modifier = modifier.fillMaxSize()
    ) {
        // Background image
        Image(
            painter = painterResource(id = R.drawable.bg),
            contentDescription = "Background Image",
            modifier = Modifier.fillMaxSize().hazeSource(hazeState),
            contentScale = ContentScale.Crop
        )

        // Demo different blur effects
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(32.dp),
            verticalArrangement = Arrangement.SpaceEvenly,
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            // Glass morphism card 1
            GlassMorphismCard(
                title = "Glass Effect 1",
                subtitle = "Light blur với gradient",
                blurRadius = 8.dp,
                hazeState = hazeState,
                alpha = 0.2f
            )

            // Glass morphism card 2
            GlassMorphismCard(
                title = "Glass Effect 2",
                subtitle = "Medium blur với border",
                blurRadius = 12.dp,
                alpha = 0.15f,
                hazeState = hazeState,
                showBorder = true
            )

            // Glass morphism card 3
            GlassMorphismCard(
                title = "Glass Effect 3",
                subtitle = "Heavy blur với shadow",
                blurRadius = 16.dp,
                alpha = 0.1f,
                hazeState = hazeState,
                showShadow = true
            )
        }
    }
}

@Composable
fun GlassMorphismCard(
    title: String,
    subtitle: String,
    blurRadius: androidx.compose.ui.unit.Dp = 10.dp,
    alpha: Float = 0.2f,
    showBorder: Boolean = false,
    showShadow: Boolean = false,
    hazeState: HazeState,
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier
            .width(280.dp)
            .height(120.dp)
    ) {
        // Glass overlay layer
        Box(
            modifier = Modifier
                .fillMaxSize().hazeEffect {  }
                .clip(RoundedCornerShape(20.dp))
                .background(
                    Brush.linearGradient(
                        colors = listOf(
                            Color.White.copy(alpha = alpha),
                            Color.White.copy(alpha = alpha * 0.7f)
                        )
                    )
                )
                .then(
                    if (showBorder) {
                        Modifier.border(
                            width = 1.dp,
                            color = Color.White.copy(alpha = 0.3f),
                            shape = RoundedCornerShape(20.dp)
                        )
                    } else Modifier
                )
                .padding(20.dp),
            contentAlignment = Alignment.Center
        ) {
            // Content layer - không bị blur
            Column(
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = title,
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color.White,
                    textAlign = TextAlign.Center
                )
                Spacer(modifier = Modifier.height(4.dp))
                Text(
                    text = subtitle,
                    fontSize = 12.sp,
                    color = Color.White.copy(alpha = 0.8f),
                    textAlign = TextAlign.Center
                )
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
fun BlurTestScreenPreview() {
    AppTheme {
        BlurTestScreen()
    }
}
