package com.truonganim.testapp.weather.ui.theme

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.selection.selectable
import androidx.compose.foundation.selection.selectableGroup
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import kotlinx.coroutines.launch

class ThemeSelectionActivity : ComponentActivity() {
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        setContent {
            val context = LocalContext.current
            val scope = rememberCoroutineScope()
            
            // Load the saved theme
            val themeFlow = ThemeManager.getThemeFlow(context)
            val currentTheme by themeFlow.collectAsState(initial = ThemeManager.THEME_SYSTEM)
            
            // Set the theme based on selection
            val darkTheme = ThemeManager.shouldUseDarkTheme(context)
            
            // Use a key to force recomposition of the entire UI when theme changes
            key(darkTheme) {
                AppTheme(darkTheme = darkTheme) {
                    ThemeSelectionScreen(
                        currentTheme = currentTheme,
                        onThemeSelected = { theme ->
                            scope.launch {
                                ThemeManager.setTheme(context, theme)
                            }
                        },
                        onBackPressed = { finish() }
                    )
                }
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ThemeSelectionScreen(
    currentTheme: String,
    onThemeSelected: (String) -> Unit,
    onBackPressed: () -> Unit
) {
    val colorScheme = MaterialTheme.colorScheme
    
    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("Theme Selection") },
                navigationIcon = {
                    IconButton(onClick = onBackPressed) {
                        Icon(
                            imageVector = Icons.Default.ArrowBack,
                            contentDescription = "Back"
                        )
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = colorScheme.background,
                    titleContentColor = colorScheme.onBackground,
                    navigationIconContentColor = colorScheme.onBackground
                )
            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = "Select Theme",
                fontSize = 20.sp,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.padding(bottom = 24.dp)
            )
            
            ThemeOptions(
                currentTheme = currentTheme,
                onThemeSelected = onThemeSelected
            )
        }
    }
}

@Composable
fun ThemeOptions(
    currentTheme: String,
    onThemeSelected: (String) -> Unit
) {
    val options = listOf(
        ThemeOption(
            id = ThemeManager.THEME_LIGHT,
            title = "Light Theme",
            description = "Light background with dark text"
        ),
        ThemeOption(
            id = ThemeManager.THEME_DARK,
            title = "Dark Theme",
            description = "Dark background with light text"
        ),
        ThemeOption(
            id = ThemeManager.THEME_SYSTEM,
            title = "System Default",
            description = "Follow system theme settings"
        )
    )
    
    Column(
        modifier = Modifier
            .selectableGroup()
            .fillMaxWidth()
    ) {
        options.forEach { option ->
            ThemeOptionItem(
                option = option,
                selected = currentTheme == option.id,
                onSelected = { onThemeSelected(option.id) }
            )
            
            if (option != options.last()) {
                Divider(
                    modifier = Modifier.padding(vertical = 8.dp),
                    color = MaterialTheme.colorScheme.outlineVariant
                )
            }
        }
    }
}

@Composable
fun ThemeOptionItem(
    option: ThemeOption,
    selected: Boolean,
    onSelected: () -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .selectable(
                selected = selected,
                onClick = onSelected,
                role = Role.RadioButton
            )
            .padding(16.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        RadioButton(
            selected = selected,
            onClick = null
        )
        
        Spacer(modifier = Modifier.width(16.dp))
        
        Column {
            Text(
                text = option.title,
                style = MaterialTheme.typography.bodyLarge
            )
            
            Text(
                text = option.description,
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}

data class ThemeOption(
    val id: String,
    val title: String,
    val description: String
) 