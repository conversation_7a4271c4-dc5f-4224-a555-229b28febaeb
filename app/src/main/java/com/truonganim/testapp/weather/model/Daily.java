package com.truonganim.testapp.weather.model;

import androidx.annotation.Nullable;

import com.google.gson.annotations.SerializedName;

import java.util.List;

/*
    next 7 days
 */
public class Daily {
    @SerializedName("time")
    private List<Long> time;
    @SerializedName("sunset")
    private List<Long> sunset;
    @SerializedName("apparent_temperature_min")
    private List<Float> apparentTemperatureMin;
    @SerializedName("apparent_temperature_max")
    private List<Float> apparentTemperatureMax;
    @SerializedName("precipitation_sum")
    private List<Float> precipitationSum;
    @SerializedName("windgusts_10m_max")
    private List<Float> windGusts10mMax;
    @SerializedName("weathercode")
    private List<Integer> weatherCode;
    @SerializedName("temperature_2m_max")
    private List<Float> temperature2mMax;
    @SerializedName("temperature_2m_min")
    private List<Float> temperature2mMin;
    @SerializedName("winddirection_10m_dominant")
    private List<Float> windDirection10mDominant;
    @SerializedName("precipitation_hours")
    private List<Float> precipitationHours;
    @SerializedName("sunrise")
    private List<Long> sunrise;
    @SerializedName("windspeed_10m_max")
    private List<Float> windSpeed10mMax;

    @Nullable
    private <T> T getValueList(List<T> list, int index) {
        if (list != null && list.size() > index && index >= 0) {
            return list.get(index);
        }
        return null;
    }

    public ItemDaily getItemDaily(int index) {
        if (index < 0 || index >= time.size()) {
            return null;
        }
        try {
            Long timeValue = getValueList(time, index);
            Long sunsetValue = getValueList(sunset, index);
            Float apparentTemperatureMinValue = getValueList(apparentTemperatureMin, index);
            Float apparentTemperatureMaxValue = getValueList(apparentTemperatureMax, index);
            Float precipitationSumValue = getValueList(precipitationSum, index);
            Float windGusts10mMaxValue = getValueList(windGusts10mMax, index);
            Integer weatherCodeValue = getValueList(weatherCode, index);
            Float temperature2mMaxValue = getValueList(temperature2mMax, index);
            Float temperature2mMinValue = getValueList(temperature2mMin, index);
            Float windDirection10mDominantValue = getValueList(windDirection10mDominant, index);
            Float precipitationHoursValue = getValueList(precipitationHours, index);
            Long sunriseValue = getValueList(sunrise, index);
            Float windSpeed10mMaxValue = getValueList(windSpeed10mMax, index);
            return new ItemDaily(
                    timeValue != null ? timeValue : 0L,
                    sunsetValue != null ? sunsetValue : 0L,
                    apparentTemperatureMinValue != null ? apparentTemperatureMinValue : 0L,
                    apparentTemperatureMaxValue != null ? apparentTemperatureMaxValue : 0L,
                    precipitationSumValue != null ? precipitationSumValue : 0f,
                    windGusts10mMaxValue != null ? windGusts10mMaxValue : 0f,
                    weatherCodeValue != null ? weatherCodeValue : 0,
                    temperature2mMaxValue != null ? temperature2mMaxValue : 0f,
                    temperature2mMinValue != null ? temperature2mMinValue : 0f,
                    windDirection10mDominantValue != null ? windDirection10mDominantValue : 0f,
                    precipitationHoursValue != null ? precipitationHoursValue : 0f,
                    sunriseValue != null ? sunriseValue : 0L,
                    windSpeed10mMaxValue != null ? windSpeed10mMaxValue : 0L
            );
        } catch (Exception e) {
            return null;
        }
    }

    public List<Long> getTime() {
        return time;
    }

    public void setTime(List<Long> time) {
        this.time = time;
    }

    public List<Long> getSunset() {
        return sunset;
    }

    public void setSunset(List<Long> sunset) {
        this.sunset = sunset;
    }

    public List<Float> getApparentTemperatureMin() {
        return apparentTemperatureMin;
    }

    public void setApparentTemperatureMin(List<Float> apparentTemperatureMin) {
        this.apparentTemperatureMin = apparentTemperatureMin;
    }

    public List<Float> getApparentTemperatureMax() {
        return apparentTemperatureMax;
    }

    public void setApparentTemperatureMax(List<Float> apparentTemperatureMax) {
        this.apparentTemperatureMax = apparentTemperatureMax;
    }

    public List<Float> getPrecipitationSum() {
        return precipitationSum;
    }

    public void setPrecipitationSum(List<Float> precipitationSum) {
        this.precipitationSum = precipitationSum;
    }

    public List<Float> getWindGusts10mMax() {
        return windGusts10mMax;
    }

    public void setWindGusts10mMax(List<Float> windGusts10mMax) {
        this.windGusts10mMax = windGusts10mMax;
    }

    public List<Integer> getWeatherCode() {
        return weatherCode;
    }

    public void setWeatherCode(List<Integer> weatherCode) {
        this.weatherCode = weatherCode;
    }

    public List<Float> getTemperature2mMax() {
        return temperature2mMax;
    }

    public void setTemperature2mMax(List<Float> temperature2mMax) {
        this.temperature2mMax = temperature2mMax;
    }

    public List<Float> getTemperature2mMin() {
        return temperature2mMin;
    }

    public void setTemperature2mMin(List<Float> temperature2mMin) {
        this.temperature2mMin = temperature2mMin;
    }

    public List<Float> getWindDirection10mDominant() {
        return windDirection10mDominant;
    }

    public void setWindDirection10mDominant(List<Float> windDirection10mDominant) {
        this.windDirection10mDominant = windDirection10mDominant;
    }

    public List<Float> getPrecipitationHours() {
        return precipitationHours;
    }

    public void setPrecipitationHours(List<Float> precipitationHours) {
        this.precipitationHours = precipitationHours;
    }

    public List<Long> getSunrise() {
        return sunrise;
    }

    public void setSunrise(List<Long> sunrise) {
        this.sunrise = sunrise;
    }

    public List<Float> getWindSpeed10mMax() {
        return windSpeed10mMax;
    }

    public void setWindSpeed10mMax(List<Float> windSpeed10mMax) {
        this.windSpeed10mMax = windSpeed10mMax;
    }
}
