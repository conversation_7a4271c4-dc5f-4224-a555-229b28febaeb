package com.truonganim.testapp.weather.model;

import com.google.gson.annotations.SerializedName;

import java.util.ArrayList;
import java.util.List;

public class ItemWeather {

    @SerializedName("current_weather")
    private Current current;
    @SerializedName("daily")
    private Daily daily;
    @SerializedName("hourly")
    private Hourly hourly;
    @SerializedName("latitude")
    private double lat;
    @SerializedName("longitude")
    private double lon;
    @SerializedName("timezone")
    private String timezone;
    @SerializedName("utc_offset_seconds")
    private int timezoneOffset;
    @SerializedName("elevation")
    private int elevation;

    public double getLat() {
        return this.lat;
    }

    public double getLon() {
        return this.lon;
    }

    public String getTimezone() {
        return this.timezone;
    }

    public void setTimezone(String str) {
        this.timezone = str;
    }

    public int getTimezoneOffset() {
        return this.timezoneOffset;
    }

    public Current getCurrent() {
        return this.current;
    }

    public Hourly getHourly() {
        return this.hourly;
    }

    public Daily getDaily() {
        return this.daily;
    }

    public ArrayList<ItemHourly> itemHourlies() {
        ArrayList<ItemHourly> results = new ArrayList<>();
        if (hourly == null) return results;
        for (int i = 0; i < this.hourly.getTime().size(); i++) {
            ItemHourly temp = this.hourly.getItemHourly(i);
            if (temp != null)
                results.add(temp);
        }
        return results;
    }

    public ArrayList<ItemDaily> itemDailies() {
        ArrayList<ItemDaily> results = new ArrayList<>();
        if (daily == null) return results;
        for (int i = 0; i < this.daily.getTime().size(); i++) {
            ItemDaily temp = this.daily.getItemDaily(i);
            if (temp != null)
                results.add(temp);
        }
        return results;
    }

    public int getCurrentWeatherCode() {
        List<ItemHourly> items = itemHourlies();
        long currentTimeMillis = System.currentTimeMillis() / 1000;
        for (ItemHourly item : items) {
            if (item != null && item.getTime() >= currentTimeMillis) {
                return item.getWeatherCode();
            }
        }
        return current != null ? current.getWeatherCode() : 0;
    }

    public float getCurrentWeatherTemp() {
        List<ItemHourly> items = itemHourlies();
        long currentTimeMillis = System.currentTimeMillis() / 1000;
        for (ItemHourly item : items) {
            if (item != null && item.getTime() >= currentTimeMillis) {
                return item.getTemperature2m();
            }
        }
        return current != null ? current.getTemp() : 0;
    }
}
