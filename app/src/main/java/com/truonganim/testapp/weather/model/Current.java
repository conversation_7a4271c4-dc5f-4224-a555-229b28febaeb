package com.truonganim.testapp.weather.model;

import com.google.gson.annotations.SerializedName;

public class Current {
    @SerializedName("time")
    private long time;
    @SerializedName("temperature")
    private float temp;;
    @SerializedName("weathercode")
    private int weatherCode;
    @SerializedName("winddirection")
    private int windDirection;
    @SerializedName("windspeed")
    private float windSpeed;

    public long getTime() {
        return time;
    }

    public void setTime(long time) {
        this.time = time;
    }

    public float getTemp() {
        return temp;
    }

    public void setTemp(float temp) {
        this.temp = temp;
    }

    public int getWeatherCode() {
        return weatherCode;
    }

    public void setWeatherCode(int weatherCode) {
        this.weatherCode = weatherCode;
    }

    public int getWindDirection() {
        return windDirection;
    }

    public void setWindDirection(int windDirection) {
        this.windDirection = windDirection;
    }

    public float getWindSpeed() {
        return windSpeed;
    }

    public void setWindSpeed(float windSpeed) {
        this.windSpeed = windSpeed;
    }
}
