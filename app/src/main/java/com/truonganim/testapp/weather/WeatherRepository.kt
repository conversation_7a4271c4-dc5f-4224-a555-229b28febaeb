package com.truonganim.testapp.weather

import android.Manifest
import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.location.Geocoder
import android.location.LocationManager
import android.os.Build
import android.util.Log
import androidx.core.app.ActivityCompat
import androidx.core.content.edit
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.truonganim.testapp.weather.model.ItemCity
import com.truonganim.testapp.weather.model.ItemWeather
import com.google.android.gms.location.LocationRequest
import com.google.android.gms.location.LocationServices
import com.google.android.gms.tasks.CancellationToken
import com.google.android.gms.tasks.OnTokenCanceledListener
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.truonganim.testapp.weather.ui.utils.getWeatherIcon
import com.truonganim.testapp.weather.ui.utils.getWeatherIconText
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.cancelChildren
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.IOException
import java.util.Locale
import kotlin.coroutines.resume
import kotlin.coroutines.suspendCoroutine

const val WEATHER_UPDATE_ACTION = "WEATHER_UPDATE_ACTION"

class WeatherRepository(private val appContext: Context) {

    private val weatherApi = WeatherApi.create()
    private val scope: CoroutineScope = CoroutineScope(Dispatchers.Main)
    private var loading: Boolean = false

    companion object {
        private const val TAG = "WeatherRepository"

        private const val WEATHER_DATA = "weather_data"
        private const val CITY_WEATHER = "city_weather"
        private const val SHOW_C_TEMP = "show_c_temp"
        private const val WEATHER_RELOAD_INTERVAL = "weather_reload_interval"
        private const val LAST_LOAD_TIME = "weather_last_load_time"

        // Hà Nội coordinates
        private const val HANOI_LAT = "21.0278"
        private const val HANOI_LON = "105.8342"
        private const val HANOI_NAME = "Hà Nội"

        @Volatile
        private var instance: WeatherRepository? = null

        @Synchronized
        fun getInstance(appContext: Context): WeatherRepository {
            if (instance == null) {
                instance = WeatherRepository(appContext.applicationContext)
            }
            return instance!!
        }
    }

    fun getCachedWeather(): ItemWeather? {
        val string: String? = Utilities.getPrefs(appContext).getString(WEATHER_DATA, "")
        return if (string.isNullOrEmpty()) {
            null
        } else try {
            Gson().fromJson<ItemWeather?>(string, object : TypeToken<ItemWeather?>() {}.type)
        } catch (e: java.lang.Exception) {
            null
        }
    }

    fun getCachedCity(): ItemCity? {
        val string: String? = Utilities.getPrefs(appContext).getString(CITY_WEATHER, "")
        return if (string.isNullOrEmpty()) {
            null
        } else Gson().fromJson<ItemCity?>(string, object : TypeToken<ItemCity?>() {}.type)
    }

    fun isShowCTemp(): Boolean {
        return Utilities.getPrefs(appContext).getBoolean(SHOW_C_TEMP, true)
    }

    fun setIsShowCTemp(show: Boolean) {
        Utilities.getPrefs(appContext)
            .edit {
                putBoolean(SHOW_C_TEMP, show)
            }
        LocalBroadcastManager.getInstance(appContext).sendBroadcast(Intent(WEATHER_UPDATE_ACTION))
    }

    fun setReloadInterval(millis: Long) {
        Utilities.getPrefs(appContext)
            .edit {
                putLong(WEATHER_RELOAD_INTERVAL, millis)
            }
        loadWeather()
    }

    val lastLoadTime: Long
        get() = Utilities.getPrefs(appContext).getLong(LAST_LOAD_TIME, 0L)

    val reloadInterval: Long
        get() = Utilities.getPrefs(appContext).getLong(WEATHER_RELOAD_INTERVAL, 86400000L)

    val canLoadWeather: Boolean
        get() = Utilities.locationPermission(appContext) && !loading &&
            (System.currentTimeMillis() - lastLoadTime > reloadInterval)

    // Generate and save daily weather summary
    fun getDailySummery(): String {
        val weather = getCachedWeather()
        val city = getCachedCity()
        if (weather == null || city == null || weather.current?.temp == null || weather.current?.weatherCode == null) {
            return "Tap to setup weather ⛅"
        }
        return generateDailySummary(weather, city)
    }

    /**
     * Generate a summary of the daily weather
     */
    private fun generateDailySummary(weather: ItemWeather, city: ItemCity): String {
        val currentTemp = if (isShowCTemp()) {
            "${weather.current?.temp!!.toInt()}°C"
        } else {
            "${((weather.current?.temp!!) * 9 / 5 + 32).toInt()}°F"
        }
        val location = city.name

        return "$location, $currentTemp"
    }

    private fun getDailyIcon(weather: ItemWeather): Int {
        return getWeatherIcon(weather.current?.weatherCode!!)
    }

    fun loadWeather() {
        Log.d(TAG, "loadWeather: $lastLoadTime $reloadInterval")
        if (!Utilities.locationPermission(appContext)) {
            return
        }
        if (loading) {
            return
        }
        loading = true
        scope.launch {
            val canLoad = withContext(Dispatchers.IO) {
                System.currentTimeMillis() - lastLoadTime > reloadInterval
            }

            if (canLoad) {
                try {
                    val location = loadLocation()
                    Log.d(TAG, "loadWeather: location $location")
                    if (location.isNotEmpty()) {
                        val address = getAddress(location[0], location[1])
                        val city = ItemCity(address, "", location[1], location[0])
                        val weather = weatherApi.loadWeather(lat = location[0], lon = location[1])
                        withContext(Dispatchers.IO) {
                            Utilities.getPrefs(appContext)
                                .edit(commit = true) {
                                    putString(WEATHER_DATA, Gson().toJson(weather))
                                        .putString(CITY_WEATHER, Gson().toJson(city))
                                        .putLong(LAST_LOAD_TIME, System.currentTimeMillis())
                                }
                        }
                        return@launch
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "loadWeather: ", e)
                }
            } else {
            }
        }.invokeOnCompletion {
            LocalBroadcastManager.getInstance(appContext)
                .sendBroadcast(Intent(WEATHER_UPDATE_ACTION))
            loading = false
        }
    }

    @SuppressLint("MissingPermission")
    suspend fun loadLocation(): Array<String> = withContext(Dispatchers.IO) {
        if (ActivityCompat.checkSelfPermission(
                appContext,
                Manifest.permission.ACCESS_FINE_LOCATION,
            ) == PackageManager.PERMISSION_GRANTED
            || ActivityCompat.checkSelfPermission(
                appContext,
                Manifest.permission.ACCESS_COARSE_LOCATION,
            ) == PackageManager.PERMISSION_GRANTED
        ) {
            val gpsLocation =
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                    (appContext.getSystemService(Context.LOCATION_SERVICE) as LocationManager)
                        .getLastKnownLocation(LocationManager.FUSED_PROVIDER)
                } else {
                    (appContext.getSystemService(Context.LOCATION_SERVICE) as LocationManager)
                        .getLastKnownLocation(LocationManager.GPS_PROVIDER)
                }
            if (gpsLocation != null) {
                return@withContext arrayOf(
                    gpsLocation.latitude.toString(),
                    gpsLocation.longitude.toString(),
                )
            }
            suspendCoroutine { continuation ->
                LocationServices.getFusedLocationProviderClient(appContext).getCurrentLocation(
                    LocationRequest.PRIORITY_LOW_POWER,
                    object : CancellationToken() {
                        override fun onCanceledRequested(p0: OnTokenCanceledListener): CancellationToken {
                            return this
                        }

                        override fun isCancellationRequested(): Boolean {
                            return false
                        }
                    },
                ).addOnSuccessListener { location ->
                    if (location == null) {
                        continuation.resume(emptyArray())
                        return@addOnSuccessListener
                    }
                    if (location.latitude == 0.0 || location.longitude == 0.0) {
                        continuation.resume(emptyArray())
                    } else {
                        continuation.resume(
                            arrayOf(
                                location.latitude.toString(),
                                location.longitude.toString(),
                            ),
                        )
                    }
                }.addOnFailureListener { ext ->
                    Log.e(TAG, "loadLocation: ", ext)
                    continuation.resume(emptyArray())
                }
            }
        } else {
            emptyArray()
        }
    }

    suspend fun getAddress(latitude: String, longitude: String): String =
        withContext(Dispatchers.IO) {
            try {
                val fromLocation = Geocoder(appContext, Locale.getDefault())
                    .getFromLocation(latitude.toDouble(), longitude.toDouble(), 1)
                if (fromLocation != null) {
                    if (fromLocation.size != 0) {
                        val address = fromLocation[0]
                        val subAdminArea = address.subAdminArea
                        if (subAdminArea != null && !subAdminArea.isEmpty()) {
                            return@withContext subAdminArea
                        }
                        val adminArea = address.adminArea
                        return@withContext if (adminArea == null || adminArea.isEmpty()) {
                            ""
                        } else adminArea
                    }
                }
            } catch (_: IOException) {
            }
            return@withContext ""
        }

    fun clear() {
        scope.coroutineContext.cancelChildren()
    }

}
