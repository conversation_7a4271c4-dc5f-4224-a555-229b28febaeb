package com.truonganim.testapp.weather.ui.components

import android.location.Location
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Button
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Switch
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.truonganim.testapp.weather.model.ItemCity
import com.truonganim.testapp.weather.model.ItemWeather
import com.truonganim.testapp.weather.ui.utils.formatDateTime
import com.truonganim.testapp.weather.ui.utils.formatTemperature
import com.truonganim.testapp.weather.ui.utils.formatTime
import com.truonganim.testapp.weather.ui.utils.getWeatherDescription

@Composable
fun WeatherContent(
    weatherData: ItemWeather?,
    cityData: ItemCity?,
    currentLocation: Location?,
    isShowCTemp: Boolean,
    onTemperatureUnitToggle: () -> Unit,
    onRefresh: () -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding( horizontal = 8.dp),
    ) {
        // Fixed top card
        CurrentWeatherCard(
            weatherData = weatherData,
            cityData = cityData,
            isShowCTemp = isShowCTemp,
            onTemperatureUnitToggle = onTemperatureUnitToggle
        )

        Spacer(modifier = Modifier.height(8.dp))

        // Scrollable content below
        LazyColumn(
            modifier = Modifier.fillMaxSize(),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            item {
                Spacer(modifier = Modifier.height(0.dp))
            }

            // Hourly forecast
            item {
                val hourlyItems = weatherData?.let { getHourlyItemsForToday(it.itemHourlies()) }
                HourlyForecastCard(
                    hourlyItems = hourlyItems,
                    isShowCTemp = isShowCTemp
                )
            }

            // Weekly forecast
            item {
                WeeklyForecastCard(
                    dailyItems = weatherData?.itemDailies(),
                    isShowCTemp = isShowCTemp
                )
            }

            // Weather details
            item {
                val firstHourly = weatherData?.itemHourlies()?.firstOrNull()
                val firstDaily = weatherData?.itemDailies()?.firstOrNull()

                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    HumidityCard(
                        modifier = Modifier.weight(1f),
                        humidity = firstHourly?.getRelativeHumidity2m(),
                        dewPoint = firstHourly?.getDewpoint2m()?.let { formatTemperature(it, isShowCTemp) }
                    )

                    FeelsLikeCard(
                        modifier = Modifier.weight(1f),
                        feelsLikeTemp = firstHourly?.getApparentTemperature()?.let { formatTemperature(it, isShowCTemp) },
                        isShowCTemp = isShowCTemp,
                        windSpeed = firstHourly?.getWindSpeed10m()?.let { "$it mph" },
                        pressure = firstHourly?.getPressureMsl()?.let { "$it hPa" },
                        apparentTemperatureMax = firstDaily?.getApparentTemperatureMax()?.let { formatTemperature(it, isShowCTemp) },
                        apparentTemperatureMin = firstDaily?.getApparentTemperatureMin()?.let { formatTemperature(it, isShowCTemp) }
                    )
                }
            }

            item {
                Spacer(modifier = Modifier.height(0.dp))
            }
        }
    }
}