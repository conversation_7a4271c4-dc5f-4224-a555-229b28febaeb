package com.truonganim.testapp.weather

import com.truonganim.testapp.weather.model.ItemWeather
import okhttp3.OkHttpClient
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import retrofit2.http.GET
import retrofit2.http.Query
import java.util.TimeZone
import java.util.concurrent.TimeUnit

interface WeatherApi {

    companion object {
        private const val API_WEATHER = "https://api.open-meteo.com/v1/"

        private const val HOURLY = "temperature_2m,relativehumidity_2m,dewpoint_2m,apparent_temperature,pressure_msl,precipitation,weathercode,cloudcover,cloudcover_low,cloudcover_mid,cloudcover_high,windspeed_10m,windspeed_80m,windspeed_120m,windspeed_180m,winddirection_10m,winddirection_80m,winddirection_120m,winddirection_180m"
        private const val DAILY = "weathercode,temperature_2m_max,temperature_2m_min,apparent_temperature_max,apparent_temperature_min,sunrise,sunset,precipitation_sum,precipitation_hours,windspeed_10m_max,windgusts_10m_max,winddirection_10m_dominant"
        private const val TEMP_UNIT = "celsius"

        fun create(): WeatherApi {
            val client = OkHttpClient.Builder()
                .connectTimeout(25, TimeUnit.SECONDS)
                .writeTimeout(60, TimeUnit.SECONDS)
                .readTimeout(30, TimeUnit.SECONDS)
                .build()
            return Retrofit.Builder()
                .baseUrl(API_WEATHER)
                .client(client)
                .addConverterFactory(GsonConverterFactory.create())
                .build()
                .create(WeatherApi::class.java)
        }
    }

    @GET("forecast")
    suspend fun loadWeather(
        @Query("latitude") lat: String,
        @Query("longitude") lon: String,
        @Query("timeformat") timeFormat: String = "unixtime",
        @Query("timezone") timezone: String = TimeZone.getDefault().id,
        @Query("hourly") hourly: String = HOURLY,
        @Query("daily") daily: String = DAILY,
        @Query("temperature_unit") tempUnit: String = TEMP_UNIT,
        @Query("current_weather") currentWeather: String = "true",
    ): ItemWeather
}