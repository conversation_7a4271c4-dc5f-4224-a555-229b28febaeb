package com.truonganim.testapp.weather.ui.utils

import android.util.Log
import com.truonganim.testapp.R
import com.truonganim.testapp.weather.model.ItemWeather
import java.text.SimpleDateFormat
import java.util.*

private const val TAG = "WeatherUtils"
enum class WeatherType {
    CLEAR,
    PARTLY_CLOUDY,
    FOG,
    DRIZZLE,
    RAIN,
    SNOW,
    RAIN_SHOWERS,
    THUNDERSTORM,
    CLOUDY
}

// Helper functions
fun formatTemperature(temp: Float, isShowCTemp: Boolean, includeDegree: Boolean = true, includeUnit: Boolean = false): String {
    val temperature = if (isShowCTemp) temp else (temp * 9 / 5) + 32
    val formattedTemp = "%.0f".format(temperature)
    val degree = if(includeDegree) "°" else ""
    return if (includeUnit) {
        "$formattedTemp${if (isShowCTemp) "${degree}C" else "${degree}F"}"
    } else {
        "$formattedTemp$degree"
    }
}

fun formatHourTime(timestamp: Long): String {
    val date = Date(timestamp * 1000)
    val format = SimpleDateFormat("HH:mm", Locale.getDefault())
    return format.format(date)
}

fun timestampToHour(timestampMillis: Long): Int {
    val calendar = Calendar.getInstance().apply {
        timeInMillis = timestampMillis * 1000
    }
    return calendar.get(Calendar.HOUR_OF_DAY)
}

fun formatTime(timestamp: Long): String {
    val date = Date(timestamp * 1000)
    val format = SimpleDateFormat("HH:mm", Locale.getDefault())
    return format.format(date)
}

fun formatDateTime(timestamp: Long): String {
    val date = Date(timestamp * 1000)
    val format = SimpleDateFormat("dd/MM HH:mm", Locale.getDefault())
    return format.format(date)
}

fun formatDayOfWeek(timestamp: Long): String {
    val date = Date(timestamp * 1000)
    val format = SimpleDateFormat("EEEE", Locale.ENGLISH)
    return format.format(date)
}


fun isDaytime(hour: Int = Calendar.getInstance().get(Calendar.HOUR_OF_DAY)): Boolean {
    return hour in 6..17
}

fun getWeatherTypeFromCode(code: Int): WeatherType {
    return when (code) {
        0 -> WeatherType.CLEAR
        1, 2, 3 -> WeatherType.PARTLY_CLOUDY
        45, 48 -> WeatherType.FOG
        51, 53, 55 -> WeatherType.DRIZZLE
        61, 63, 65 -> WeatherType.RAIN
        71, 73, 75 -> WeatherType.SNOW
        80, 81, 82 -> WeatherType.RAIN_SHOWERS
        95, 96, 99 -> WeatherType.THUNDERSTORM
        else -> WeatherType.CLOUDY
    }
}

fun getWeatherIconText(code: Int): String {
    val type = getWeatherTypeFromCode(code)
    return when (type) {
        WeatherType.CLEAR -> "☀️" // Clear sky
        WeatherType.PARTLY_CLOUDY -> "⛅" // Partly cloudy
        WeatherType.FOG -> "🌫️" // Fog
        WeatherType.DRIZZLE -> "🌧️" // Drizzle
        WeatherType.RAIN -> "🌧️" // Rain
        WeatherType.SNOW -> "❄️" // Snow
        WeatherType.RAIN_SHOWERS -> "🌧️" // Rain showers
        WeatherType.THUNDERSTORM -> "⛈️" // Thunderstorm
        else -> "☁️" // Cloudy
    }
}

fun getWeatherIcon(code: Int, hour: Int = Calendar.getInstance().get(Calendar.HOUR_OF_DAY)): Int {
    val type = getWeatherTypeFromCode(code)
    if(isDaytime(hour)){
        return when (type) {
            WeatherType.CLEAR -> R.drawable.icon_clear // Clear sky
            WeatherType.PARTLY_CLOUDY -> R.drawable.icon_partly_cloudy // Partly cloudy
            WeatherType.FOG -> R.drawable.icon_fog // Fog
            WeatherType.DRIZZLE -> R.drawable.icon_rain // Drizzle
            WeatherType.RAIN -> R.drawable.icon_rain // Rain
            WeatherType.SNOW -> R.drawable.icon_snow // Snow
            WeatherType.RAIN_SHOWERS -> R.drawable.icon_thunderstorm // Rain showers
            WeatherType.THUNDERSTORM -> R.drawable.icon_thunderstorm // Thunderstorm
            else -> R.drawable.icon_cloud // Cloudy
        }
    }
    return when (type) {
        WeatherType.CLEAR -> R.drawable.icon_moon // Clear sky
        WeatherType.PARTLY_CLOUDY -> R.drawable.icon_moon // Partly cloudy
        WeatherType.FOG -> R.drawable.icon_fog // Fog
        WeatherType.DRIZZLE -> R.drawable.icon_rain // Drizzle
        WeatherType.RAIN -> R.drawable.icon_rain // Rain
        WeatherType.SNOW -> R.drawable.icon_snow // Snow
        WeatherType.RAIN_SHOWERS -> R.drawable.icon_thunderstorm // Rain showers
        WeatherType.THUNDERSTORM -> R.drawable.icon_thunderstorm // Thunderstorm
        else -> R.drawable.icon_cloud // Cloudy
    }
}

fun getWeatherDescription(code: Int): String {
    val type = getWeatherTypeFromCode(code)
    return when (type) {
        WeatherType.CLEAR -> "Clear sky"
        WeatherType.PARTLY_CLOUDY -> "Partly cloudy"
        WeatherType.FOG -> "Fog"
        WeatherType.DRIZZLE -> "Drizzle"
        WeatherType.RAIN -> "Rain"
        WeatherType.SNOW -> "Snow"
        WeatherType.RAIN_SHOWERS -> "Rain showers"
        WeatherType.THUNDERSTORM -> "Thunderstorm"
        else -> "Unknown"
    }
}


fun getWeatherBackground(code: Int): Int {
    val type = getWeatherTypeFromCode(code)
    if (isDaytime()){
        return when (type) {
            WeatherType.CLEAR -> R.drawable.clear_sky
            WeatherType.PARTLY_CLOUDY -> R.drawable.cloudy
            WeatherType.FOG -> R.drawable.fog
            WeatherType.DRIZZLE -> R.drawable.rain
            WeatherType.RAIN -> R.drawable.rain
            WeatherType.SNOW -> R.drawable.snow
            WeatherType.RAIN_SHOWERS -> R.drawable.rain_showers
            WeatherType.THUNDERSTORM -> R.drawable.thunderstorm
            else -> R.drawable.clear_sky
        }
    }
    return when (type) {
        WeatherType.CLEAR -> R.drawable.dark_mode_clear_sky
        WeatherType.PARTLY_CLOUDY -> R.drawable.dark_mode_cloudy
        WeatherType.FOG -> R.drawable.dark_mode_clear_sky
        WeatherType.DRIZZLE -> R.drawable.dark_mode_rain
        WeatherType.RAIN -> R.drawable.dark_mode_rain
        WeatherType.SNOW -> R.drawable.dark_mode_snow
        WeatherType.RAIN_SHOWERS -> R.drawable.rain_showers
        WeatherType.THUNDERSTORM -> R.drawable.dark_mode_thunderstorm
        else -> R.drawable.dark_mode_clear_sky
    }
}

// Function to log all available weather data fields
fun logWeatherData(weather: ItemWeather) {
    Log.d(TAG, "--- WEATHER DATA LOG START ---")
    
    // Log basic info
    Log.d(TAG, "Latitude: ${weather.getLat()}")
    Log.d(TAG, "Longitude: ${weather.getLon()}")
    Log.d(TAG, "Timezone: ${weather.getTimezone()}")
    Log.d(TAG, "Timezone Offset: ${weather.getTimezoneOffset()}")
    
    // Log current weather
    val current = weather.getCurrent()
    if (current != null) {
        Log.d(TAG, "--- CURRENT WEATHER ---")
        Log.d(TAG, "Time: ${current.getTime()}")
        Log.d(TAG, "Temperature: ${current.getTemp()}")
        Log.d(TAG, "Weather Code: ${current.getWeatherCode()}")
        Log.d(TAG, "Wind Direction: ${current.getWindDirection()}")
        Log.d(TAG, "Wind Speed: ${current.getWindSpeed()}")
    } else {
        Log.d(TAG, "Current weather data is null")
    }
    
    // Log hourly data (first item)
    val hourlyItems = weather.itemHourlies()
    if (hourlyItems.isNotEmpty()) {
        val firstHourly = hourlyItems[0]
        Log.d(TAG, "--- HOURLY DATA (First Hour) ---")
        Log.d(TAG, "Time: ${firstHourly.getTime()}")
        Log.d(TAG, "Temperature: ${firstHourly.getTemperature2m()}")
        Log.d(TAG, "Apparent Temperature: ${firstHourly.getApparentTemperature()}")
        Log.d(TAG, "Humidity: ${firstHourly.getRelativeHumidity2m()}%")
        Log.d(TAG, "Pressure: ${firstHourly.getPressureMsl()} hPa")
        Log.d(TAG, "Dew Point: ${firstHourly.getDewpoint2m()}")
        Log.d(TAG, "Weather Code: ${firstHourly.getWeatherCode()}")
        Log.d(TAG, "Cloud Cover: ${firstHourly.getCloudCover()}%")
        Log.d(TAG, "Low Cloud Cover: ${firstHourly.getCloudCoverLow()}%")
        Log.d(TAG, "Mid Cloud Cover: ${firstHourly.getCloudCoverMid()}%")
        Log.d(TAG, "High Cloud Cover: ${firstHourly.getCloudCoverHigh()}%")
        Log.d(TAG, "Wind Speed (10m): ${firstHourly.getWindSpeed10m()} km/h")
        Log.d(TAG, "Wind Direction (10m): ${firstHourly.getWindDirection10m()}°")
        Log.d(TAG, "Wind Speed (80m): ${firstHourly.getWindSpeed80m()} km/h")
        Log.d(TAG, "Wind Direction (80m): ${firstHourly.getWindDirection80m()}°")
        Log.d(TAG, "Wind Speed (120m): ${firstHourly.getWindSpeed120m()} km/h")
        Log.d(TAG, "Wind Direction (120m): ${firstHourly.getWindDirection120m()}°")
        Log.d(TAG, "Wind Speed (180m): ${firstHourly.getWindSpeed180m()} km/h")
        Log.d(TAG, "Wind Direction (180m): ${firstHourly.getWindDirection180m()}°")
    } else {
        Log.d(TAG, "Hourly data is empty")
    }
    
    // Log daily data (first day)
    val dailyItems = weather.itemDailies()
    if (dailyItems.isNotEmpty()) {
        val firstDaily = dailyItems[0]
        Log.d(TAG, "--- DAILY DATA (Today) ---")
        Log.d(TAG, "Date: ${firstDaily.getTime()}")
        Log.d(TAG, "Weather Code: ${firstDaily.getWeatherCode()}")
        Log.d(TAG, "Max Temperature: ${firstDaily.getTemperature2mMax()}°")
        Log.d(TAG, "Min Temperature: ${firstDaily.getTemperature2mMin()}°")
        Log.d(TAG, "Apparent Max Temperature: ${firstDaily.getApparentTemperatureMax()}°")
        Log.d(TAG, "Apparent Min Temperature: ${firstDaily.getApparentTemperatureMin()}°")
        Log.d(TAG, "Precipitation Sum: ${firstDaily.getPrecipitationSum()} mm")
        Log.d(TAG, "Precipitation Hours: ${firstDaily.getPrecipitationHours()} h")
        Log.d(TAG, "Max Wind Speed: ${firstDaily.getWindSpeed10mMax()} km/h")
        Log.d(TAG, "Max Wind Gusts: ${firstDaily.getWindGusts10mMax()} km/h")
        Log.d(TAG, "Wind Direction: ${firstDaily.getWindDirection10mDominant()}°")
        Log.d(TAG, "Sunrise: ${firstDaily.getSunrise()}")
        Log.d(TAG, "Sunset: ${firstDaily.getSunset()}")
    } else {
        Log.d(TAG, "Daily data is empty")
    }
    
    Log.d(TAG, "--- WEATHER DATA LOG END ---")
} 