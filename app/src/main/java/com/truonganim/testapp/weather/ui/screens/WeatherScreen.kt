package com.truonganim.testapp.weather.ui.screens

import android.Manifest
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.location.Location
import android.net.Uri
import android.provider.Settings
import android.util.Log
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.LocationOn
import androidx.compose.material.icons.filled.Settings
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.core.content.ContextCompat.startActivity
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.truonganim.testapp.R
import com.truonganim.testapp.weather.WEATHER_UPDATE_ACTION
import com.truonganim.testapp.weather.WeatherRepository
import com.truonganim.testapp.weather.ui.components.WeatherContent
import com.truonganim.testapp.weather.ui.theme.AppTheme
import com.truonganim.testapp.weather.ui.utils.*
import com.truonganim.testapp.weather.ui.viewmodel.WeatherViewModel

private const val TAG = "WeatherScreen"

@Composable
fun WeatherScreen() {
    val context = LocalContext.current
    val viewModel: WeatherViewModel = viewModel(factory = WeatherViewModel.Factory(context))
    val weatherRepository = remember { WeatherRepository.getInstance(context) }
    
    // Track if we've returned from settings
    var hasReturnedFromSettings by remember { mutableStateOf(false) }
    var hasReturnedFromLocationSettings by remember { mutableStateOf(false) }
    
    // Permission launcher
    val requestPermissionLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.RequestPermission(),
        onResult = { isGranted ->
            viewModel.onPermissionResult(isGranted)
        }
    )
    
    // Register broadcast receiver for weather updates
    DisposableEffect(key1 = Unit) {
        val receiver = object : BroadcastReceiver() {
            override fun onReceive(context: Context?, intent: Intent?) {
                viewModel.onWeatherDataUpdated(
                    weather = weatherRepository.getCachedWeather(),
                    city = weatherRepository.getCachedCity()
                )
            }
        }
        
        val filter = IntentFilter(WEATHER_UPDATE_ACTION)
        LocalBroadcastManager.getInstance(context).registerReceiver(receiver, filter)
        
        onDispose {
            LocalBroadcastManager.getInstance(context).unregisterReceiver(receiver)
        }
    }
    
    // Effect to check permissions when returning from settings
    LaunchedEffect(hasReturnedFromSettings) {
        if (hasReturnedFromSettings) {
            viewModel.checkPermissionAfterSettings()
            hasReturnedFromSettings = false
        }
    }
    
    // Effect to check location settings when returning from location settings
    LaunchedEffect(hasReturnedFromLocationSettings) {
        if (hasReturnedFromLocationSettings) {
            viewModel.checkSystemLocationSettings()
            hasReturnedFromLocationSettings = false
        }
    }

    // Get the background image resource based on current weather
    val backgroundRes = viewModel.weatherData
        ?.getCurrentWeatherCode()
        ?.let { getWeatherBackground(it) }
        ?: R.drawable.clear_sky

    Box(modifier = Modifier.fillMaxSize().navigationBarsPadding()) {
        // Background image based on weather condition
        Image(
            painter = painterResource(id = backgroundRes),
            contentDescription = "Weather Background",
            modifier = Modifier.fillMaxSize(),
            contentScale = ContentScale.Crop
        )

        // Always show weather content, even with null data
        WeatherContent(
            weatherData = viewModel.weatherData,
            cityData = viewModel.cityData,
            currentLocation = viewModel.currentLocation,
            isShowCTemp = viewModel.isShowCTemp,
            onTemperatureUnitToggle = { viewModel.toggleTemperatureUnit() },
            onRefresh = { viewModel.refreshWeather() }
        )
        
        // Show loading indicator overlay when loading
        if (viewModel.isLoading) {
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .background(MaterialTheme.colorScheme.background.copy(alpha = 0.4f)),
                contentAlignment = Alignment.Center
            ) {
                Column(horizontalAlignment = Alignment.CenterHorizontally) {
                    CircularProgressIndicator(color = MaterialTheme.colorScheme.onBackground)
                    Spacer(modifier = Modifier.height(16.dp))
                    Text(
                        "Loading weather data...",
                        color = MaterialTheme.colorScheme.onBackground,
                        style = MaterialTheme.typography.bodyLarge
                    )
                }
            }
        }
        
        // Show permission request popup if needed
        if (!viewModel.hasLocationPermission) {
            LocationPermissionPopup(
                isPermanentlyDenied = viewModel.isPermanentlyDenied,
                onRequestPermission = {
                    requestPermissionLauncher.launch(Manifest.permission.ACCESS_FINE_LOCATION)
                },
                onOpenSettings = {
                    val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS).apply {
                        data = Uri.fromParts("package", context.packageName, null)
                        addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                    }
                    startActivity(context, intent, null)
                    hasReturnedFromSettings = true
                    Log.d(TAG, "Opening settings for permissions")
                }
            )
        }
        // Show system location settings popup if app has permission but system location is disabled
        else if (!viewModel.isSystemLocationEnabled) {
            SystemLocationSettingsPopup(
                onOpenLocationSettings = {
                    val intent = Intent(Settings.ACTION_LOCATION_SOURCE_SETTINGS)
                    startActivity(context, intent, null)
                    hasReturnedFromLocationSettings = true
                    Log.d(TAG, "Opening system location settings")
                }
            )
        }
    }
}

@Composable
fun LocationPermissionPopup(
    isPermanentlyDenied: Boolean,
    onRequestPermission: () -> Unit,
    onOpenSettings: () -> Unit
) {
    Box(
        modifier = Modifier
            .fillMaxSize()
            .padding(vertical = 16.dp, horizontal = 8.dp),
        contentAlignment = Alignment.BottomCenter
    ) {
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(bottom = 16.dp),
            shape = RoundedCornerShape(24.dp),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.background.copy(alpha = 0.95f)
            )
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                horizontalAlignment = Alignment.Start
            ) {
                Box(
                    modifier = Modifier.fillMaxWidth()
                ) {
                    // Cloud icon in top left
                    Image(
                        painter = painterResource(id = R.drawable.weather_icon),
                        contentDescription = "Location",
                        modifier = Modifier
                            .size(48.dp)
                            .align(Alignment.CenterStart)
                    )
                }
                
                Spacer(modifier = Modifier.height(16.dp))
                
                Text(
                    text = "Location Access Required",
                    fontSize = 20.sp,
                    fontWeight = FontWeight.Bold,
                    textAlign = TextAlign.Left,
                    color = MaterialTheme.colorScheme.onBackground
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                Text(
                    text = "We need your location to show local weather updates.\nPlease allow access to continue.",
                    textAlign = TextAlign.Left,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    fontWeight = FontWeight.Medium
                )
                
                Spacer(modifier = Modifier.height(24.dp))
                
                Button(
                    onClick = if (isPermanentlyDenied) onOpenSettings else onRequestPermission,
                    modifier = Modifier.fillMaxWidth().height(48.dp),
                    colors = ButtonDefaults.buttonColors(
                        containerColor = MaterialTheme.colorScheme.primary
                    )
                ) {
                    Text(
                        text = if (isPermanentlyDenied) "Open Settings" else "Grant location permission",
                        color = Color.White
                    )
                }
            }
        }
    }
}

@Composable
fun SystemLocationSettingsPopup(
    onOpenLocationSettings: () -> Unit
) {
    Box(
        modifier = Modifier
            .fillMaxSize()
            .padding(vertical = 16.dp, horizontal = 8.dp),
        contentAlignment = Alignment.BottomCenter
    ) {
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(bottom = 16.dp),
            shape = RoundedCornerShape(24.dp),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.background.copy(alpha = 0.95f)
            )
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                horizontalAlignment = Alignment.Start
            ) {
                Box(
                    modifier = Modifier.fillMaxWidth()
                ) {
                    // Location icon in top left
                    Icon(
                        imageVector = Icons.Default.LocationOn,
                        contentDescription = "Location Services",
                        modifier = Modifier
                            .size(48.dp)
                            .align(Alignment.CenterStart),
                        tint = MaterialTheme.colorScheme.primary
                    )
                }
                
                Spacer(modifier = Modifier.height(16.dp))
                
                Text(
                    text = "Location Services Disabled",
                    fontSize = 20.sp,
                    fontWeight = FontWeight.Bold,
                    textAlign = TextAlign.Left,
                    color = MaterialTheme.colorScheme.onBackground
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                Text(
                    text = "Your device's location services are turned off. Please enable them in system settings to get accurate weather data for your location.",
                    textAlign = TextAlign.Left,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    fontWeight = FontWeight.Medium
                )
                
                Spacer(modifier = Modifier.height(24.dp))
                
                Button(
                    onClick = onOpenLocationSettings,
                    modifier = Modifier.fillMaxWidth().height(48.dp),
                    colors = ButtonDefaults.buttonColors(
                        containerColor = MaterialTheme.colorScheme.primary
                    )
                ) {
                    Text(
                        text = "Open Location Settings",
                        color = Color.White
                    )
                }
            }
        }
    }
}