package com.truonganim.testapp.weather.model;

import androidx.annotation.Nullable;

import com.google.gson.annotations.SerializedName;

import java.util.List;

public class Hourly {

    @SerializedName("winddirection_10m")
    private List<Integer> windDirection10m;
    @SerializedName("relativehumidity_2m")
    private List<Integer> relativeHumidity2m;
    @SerializedName("apparent_temperature")
    private List<Float> apparentTemperature;
    @SerializedName("windspeed_120m")
    private List<Float> windSpeed120m;
    @SerializedName("dewpoint_2m")
    private List<Float> dewpoint2m;
    @SerializedName("windspeed_180m")
    private List<Float> windSpeed180m;
    @SerializedName("time")
    private List<Long> time;
    @SerializedName("cloudcover_mid")
    private List<Integer> cloudCoverMid;
    @SerializedName("cloudcover_low")
    private List<Integer> cloudCoverLow;
    @SerializedName("cloudcover_high")
    private List<Integer> cloudCoverHigh;
    @SerializedName("cloudcover")
    private List<Integer> cloudCover;
    @SerializedName("windspeed_10m")
    private List<Float> windSpeed10m;
    @SerializedName("winddirection_120m")
    private List<Integer> windDirection120m;
    @SerializedName("weathercode")
    private List<Integer> weatherCode;
    @SerializedName("winddirection_80m")
    private List<Integer> windDirection80m;
    @SerializedName("winddirection_180m")
    private List<Integer> windDirection180m;
    @SerializedName("windspeed_80m")
    private List<Float> windSpeed80m;
    @SerializedName("temperature_2m")
    private List<Float> temperature2m;
    @SerializedName("pressure_msl")
    private List<Float> pressureMsl;

    @Nullable
    private <T> T getValueList(List<T> list, int index) {
        if (list != null && list.size() > index && index >= 0) {
            return list.get(index);
        }
        return null;
    }

    public ItemHourly getItemHourly(int index) {
        if (index < 0 || index >= time.size()) {
            return null;
        }
        try {
            Integer windDirection10mValue = getValueList(windDirection10m, index);
            Integer relativeHumidity2mValue = getValueList(relativeHumidity2m, index);
            Float apparentTemperatureValue = getValueList(apparentTemperature, index);
            Float windSpeed120mValue = getValueList(windSpeed120m, index);
            Float dewpoint2mValue = getValueList(dewpoint2m, index);
            Float windSpeed180mValue = getValueList(windSpeed180m, index);
            Long timeValue = getValueList(time, index);
            Integer cloudCoverMidValue = getValueList(cloudCoverMid, index);
            Integer cloudCoverLowValue = getValueList(cloudCoverLow, index);
            Integer cloudCoverHighValue = getValueList(cloudCoverHigh, index);
            Integer cloudCoverValue = getValueList(cloudCover, index);
            Float windSpeed10mValue = getValueList(windSpeed10m, index);
            Integer windDirection120mValue = getValueList(windDirection120m, index);
            Integer weatherCodeValue = getValueList(weatherCode, index);
            Integer windDirection80mValue = getValueList(windDirection80m, index);
            Integer windDirection180mValue = getValueList(windDirection180m, index);
            Float windSpeed80mValue = getValueList(windSpeed80m, index);
            Float temperature2mValue = getValueList(temperature2m, index);
            Float pressureMslValue = getValueList(pressureMsl, index);


            return new ItemHourly(
                    windDirection10mValue != null ? windDirection10mValue : 0,
                    relativeHumidity2mValue != null ? relativeHumidity2mValue : 0,
                    apparentTemperatureValue != null ? apparentTemperatureValue : 0,
                    windSpeed120mValue != null ? windSpeed120mValue : 0,
                    dewpoint2mValue != null ? dewpoint2mValue : 0,
                    windSpeed180mValue != null ? windSpeed180mValue : 0,
                    timeValue != null ? timeValue : 0,
                    cloudCoverMidValue != null ? cloudCoverMidValue : 0,
                    cloudCoverLowValue != null ? cloudCoverLowValue : 0,
                    cloudCoverHighValue != null ? cloudCoverHighValue : 0,
                    cloudCoverValue != null ? cloudCoverValue : 0,
                    windSpeed10mValue != null ? windSpeed10mValue : 0,
                    windDirection120mValue != null ? windDirection120mValue : 0,
                    weatherCodeValue != null ? weatherCodeValue : 0,
                    windDirection80mValue != null ? windDirection80mValue : 0,
                    windDirection180mValue != null ? windDirection180mValue : 0,
                    windSpeed80mValue != null ? windSpeed80mValue : 0,
                    temperature2mValue != null ? temperature2mValue : 0,
                    pressureMslValue != null ? pressureMslValue : 0
            );
        } catch (Exception e) {
            return null;
        }
    }

    public List<Integer> getWindDirection10m() {
        return windDirection10m;
    }

    public void setWindDirection10m(List<Integer> windDirection10m) {
        this.windDirection10m = windDirection10m;
    }

    public List<Integer> getRelativeHumidity2m() {
        return relativeHumidity2m;
    }

    public void setRelativeHumidity2m(List<Integer> relativeHumidity2m) {
        this.relativeHumidity2m = relativeHumidity2m;
    }

    public List<Float> getApparentTemperature() {
        return apparentTemperature;
    }

    public void setApparentTemperature(List<Float> apparentTemperature) {
        this.apparentTemperature = apparentTemperature;
    }

    public List<Float> getWindSpeed120m() {
        return windSpeed120m;
    }

    public void setWindSpeed120m(List<Float> windSpeed120m) {
        this.windSpeed120m = windSpeed120m;
    }

    public List<Float> getDewpoint2m() {
        return dewpoint2m;
    }

    public void setDewpoint2m(List<Float> dewpoint2m) {
        this.dewpoint2m = dewpoint2m;
    }

    public List<Float> getWindSpeed180m() {
        return windSpeed180m;
    }

    public void setWindSpeed180m(List<Float> windSpeed180m) {
        this.windSpeed180m = windSpeed180m;
    }

    public List<Long> getTime() {
        return time;
    }

    public void setTime(List<Long> time) {
        this.time = time;
    }

    public List<Integer> getCloudCoverMid() {
        return cloudCoverMid;
    }

    public void setCloudCoverMid(List<Integer> cloudCoverMid) {
        this.cloudCoverMid = cloudCoverMid;
    }

    public List<Integer> getCloudCoverLow() {
        return cloudCoverLow;
    }

    public void setCloudCoverLow(List<Integer> cloudCoverLow) {
        this.cloudCoverLow = cloudCoverLow;
    }

    public List<Integer> getCloudCoverHigh() {
        return cloudCoverHigh;
    }

    public void setCloudCoverHigh(List<Integer> cloudCoverHigh) {
        this.cloudCoverHigh = cloudCoverHigh;
    }

    public List<Integer> getCloudCover() {
        return cloudCover;
    }

    public void setCloudCover(List<Integer> cloudCover) {
        this.cloudCover = cloudCover;
    }

    public List<Float> getWindSpeed10m() {
        return windSpeed10m;
    }

    public void setWindSpeed10m(List<Float> windSpeed10m) {
        this.windSpeed10m = windSpeed10m;
    }

    public List<Integer> getWindDirection120m() {
        return windDirection120m;
    }

    public void setWindDirection120m(List<Integer> windDirection120m) {
        this.windDirection120m = windDirection120m;
    }

    public List<Integer> getWeatherCode() {
        return weatherCode;
    }

    public void setWeatherCode(List<Integer> weatherCode) {
        this.weatherCode = weatherCode;
    }

    public List<Integer> getWindDirection80m() {
        return windDirection80m;
    }

    public void setWindDirection80m(List<Integer> windDirection80m) {
        this.windDirection80m = windDirection80m;
    }

    public List<Integer> getWindDirection180m() {
        return windDirection180m;
    }

    public void setWindDirection180m(List<Integer> windDirection180m) {
        this.windDirection180m = windDirection180m;
    }

    public List<Float> getWindSpeed80m() {
        return windSpeed80m;
    }

    public void setWindSpeed80m(List<Float> windSpeed80m) {
        this.windSpeed80m = windSpeed80m;
    }

    public List<Float> getTemperature2m() {
        return temperature2m;
    }

    public void setTemperature2m(List<Float> temperature2m) {
        this.temperature2m = temperature2m;
    }

    public List<Float> getPressureMsl() {
        return pressureMsl;
    }

    public void setPressureMsl(List<Float> pressureMsl) {
        this.pressureMsl = pressureMsl;
    }
}
