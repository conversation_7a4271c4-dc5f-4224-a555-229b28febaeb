package com.truonganim.testapp.weather.ui.components

import androidx.compose.foundation.background
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.truonganim.testapp.R
import com.truonganim.testapp.weather.model.ItemDaily
import com.truonganim.testapp.weather.ui.utils.formatDayOfWeek
import com.truonganim.testapp.weather.ui.utils.formatTemperature
import com.truonganim.testapp.weather.ui.utils.getWeatherIcon
import com.truonganim.testapp.weather.ui.utils.getWeatherIconText

@Composable
fun WeeklyForecastCard(
    dailyItems: List<ItemDaily>?,
    isShowCTemp: Boolean
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(0.dp),
        shape = RoundedCornerShape(24.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.background.copy(alpha = 0.3f)
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            Text(
                text = "7-Day Forecast",
                fontSize = 16.sp,
                fontWeight = FontWeight.Bold,
                color = Color.White.copy(alpha = 0.4f),
                modifier = Modifier.padding(bottom = 8.dp)
            )

            Spacer(modifier = Modifier.height(8.dp))

            if (!dailyItems.isNullOrEmpty()) {
                // Find global min and max temperatures across all days for the temperature slider
                val globalMinTemp = dailyItems.minOfOrNull { it.getTemperature2mMin() } ?: 0f
                val globalMaxTemp = dailyItems.maxOfOrNull { it.getTemperature2mMax() } ?: 0f
                val temperatureRange = globalMaxTemp - globalMinTemp

                dailyItems.forEach { daily ->
                    DailyForecastRow(
                        daily = daily,
                        isShowCTemp = isShowCTemp,
                        globalMinTemp = globalMinTemp,
                        globalMaxTemp = globalMaxTemp,
                        temperatureRange = temperatureRange
                    )
                    
                    if (daily != dailyItems.last()) {
                        Spacer(modifier = Modifier.height(16.dp))
                    }
                }
            } else {
                // No data state
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(120.dp),
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.Center
                ) {
                    Image(
                        painter = painterResource(id = R.drawable.cloud_error),
                        contentDescription = "No data",
                        modifier = Modifier.size(64.dp)
                    )
                    Spacer(modifier = Modifier.height(16.dp))
                    Text(
                        text = "No information displayed",
                        fontSize = 16.sp,
                        color = Color.White,
                        textAlign = TextAlign.Center
                    )
                }
            }
        }
    }
}

@Composable
fun DailyForecastRow(
    daily: ItemDaily,
    isShowCTemp: Boolean,
    globalMinTemp: Float,
    globalMaxTemp: Float,
    temperatureRange: Float
) {
    val minTemp = daily.getTemperature2mMin()
    val maxTemp = daily.getTemperature2mMax()
    
    // Calculate the position and width of the temperature slider
    val sliderStartPercent = if (temperatureRange > 0) {
        ((minTemp - globalMinTemp) / temperatureRange) * 100f
    } else 0f
    
    val sliderWidthPercent = if (temperatureRange > 0) {
        ((maxTemp - minTemp) / temperatureRange) * 100f
    } else 0f
    
    // Define gradient colors for the temperature indicator
    val gradientColors = listOf(
        Color(0xAAFFF6DB),
        Color(0xAAFF6607)  // Orange
    )
    
    Row(
        modifier = Modifier.fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically
    ) {
        // Day name (3 chars)
        val dayName = formatDayOfWeek(daily.getTime() ?: 0)
        Text(
            text = if (dayName.length > 3) dayName.substring(0, 3) else dayName,
            fontSize = 14.sp,
            color = Color.White,
            modifier = Modifier.width(40.dp)
        )

        Spacer(modifier = Modifier.width(20.dp))

        Image(
            painter = painterResource(id = getWeatherIcon(daily.getWeatherCode() ?: 0,  hour = 12)),
            contentDescription = null,
            modifier = Modifier.size(18.dp)
        )

        Spacer(modifier = Modifier.width(20.dp))

        // Min temperature
        Text(
            text = formatTemperature(minTemp, isShowCTemp),
            fontSize = 14.sp,
            color = Color.White.copy(alpha = 0.4f),
            fontWeight = FontWeight.Medium,
            textAlign = TextAlign.End
        )
        
        // Temperature slider
        Box(
            modifier = Modifier
                .weight(1f)
                .height(8.dp)
                .padding(horizontal = 8.dp)
        ) {
            // Background track
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(4.dp)
                    .clip(RoundedCornerShape(2.dp))
                    .background(Color.White.copy(alpha = 0.2f))
                    .align(Alignment.Center)
            )
            
            // Temperature range indicator with gradient
            Box(
                modifier = Modifier
                    .fillMaxWidth(sliderWidthPercent / 100f)
                    .height(4.dp)
                    .offset(x = (sliderStartPercent / 100f * (temperatureRange * 2)).dp)
                    .clip(RoundedCornerShape(2.dp))
                    .background(
                        brush = Brush.horizontalGradient(
                            colors = gradientColors
                        )
                    )
                    .align(Alignment.CenterStart)
            )
        }
        
        // Max temperature
        Text(
            text = formatTemperature(maxTemp, isShowCTemp),
            fontSize = 14.sp,
            fontWeight = FontWeight.Medium,
            color = Color.White,
        )
    }
} 