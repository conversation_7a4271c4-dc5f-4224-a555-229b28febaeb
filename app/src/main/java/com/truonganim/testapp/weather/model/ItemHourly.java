package com.truonganim.testapp.weather.model;

public class ItemHourly {

    private Integer windDirection10m;
    private Integer relativeHumidity2m;
    private Float apparentTemperature;
    private Float windSpeed120m;
    private Float dewpoint2m;
    private Float windSpeed180m;
    private Long time;
    private Integer cloudCoverMid;
    private Integer cloudCoverLow;
    private Integer cloudCoverHigh;
    private Integer cloudCover;
    private Float windSpeed10m;
    private Integer windDirection120m;
    private Integer weatherCode;
    private Integer windDirection80m;
    private Integer windDirection180m;
    private Float windSpeed80m;
    private Float temperature2m;
    private Float pressureMsl;

    public ItemHourly(Integer windDirection10m, Integer relativeHumidity2m,
                      Float apparentTemperature, Float windSpeed120m,
                      Float dewpoint2m, Float windSpeed180m, Long time, Integer cloudCoverMid,
                      Integer cloudCoverLow, Integer cloudCoverHigh, Integer cloudCover,
                      Float windSpeed10m, Integer windDirection120m, Integer weatherCode,
                      Integer windDirection80m, Integer windDirection180m,
                      Float windSpeed80m, Float temperature2m, Float pressureMsl) {
        this.windDirection10m = windDirection10m;
        this.relativeHumidity2m = relativeHumidity2m;
        this.apparentTemperature = apparentTemperature;
        this.windSpeed120m = windSpeed120m;
        this.dewpoint2m = dewpoint2m;
        this.windSpeed180m = windSpeed180m;
        this.time = time;
        this.cloudCoverMid = cloudCoverMid;
        this.cloudCoverLow = cloudCoverLow;
        this.cloudCoverHigh = cloudCoverHigh;
        this.cloudCover = cloudCover;
        this.windSpeed10m = windSpeed10m;
        this.windDirection120m = windDirection120m;
        this.weatherCode = weatherCode;
        this.windDirection80m = windDirection80m;
        this.windDirection180m = windDirection180m;
        this.windSpeed80m = windSpeed80m;
        this.temperature2m = temperature2m;
        this.pressureMsl = pressureMsl;
    }

    public ItemHourly(Integer windDirection10m, Integer relativeHumidity2m, Float apparentTemperature, Float windSpeed120m, Float dewpoint2m, Float windSpeed180m, Long time, Integer cloudCoverMid, Integer cloudCoverLow, Integer cloudCoverHigh, Integer cloudCover, Float windSpeed10m, Integer windDirection120m, Integer weatherCode, Integer windDirection80m, Integer windDirection180m, Float windSpeed80m) {
    }

    public Integer getWindDirection10m() {
        return windDirection10m;
    }

    public void setWindDirection10m(Integer windDirection10m) {
        this.windDirection10m = windDirection10m;
    }

    public Integer getRelativeHumidity2m() {
        return relativeHumidity2m;
    }

    public void setRelativeHumidity2m(Integer relativeHumidity2m) {
        this.relativeHumidity2m = relativeHumidity2m;
    }

    public Float getWindSpeed120m() {
        return windSpeed120m;
    }

    public void setWindSpeed120m(Float windSpeed120m) {
        this.windSpeed120m = windSpeed120m;
    }

    public Float getDewpoint2m() {
        return dewpoint2m;
    }

    public void setDewpoint2m(Float dewpoint2m) {
        this.dewpoint2m = dewpoint2m;
    }

    public Float getWindSpeed180m() {
        return windSpeed180m;
    }

    public void setWindSpeed180m(Float windSpeed180m) {
        this.windSpeed180m = windSpeed180m;
    }

    public Long getTime() {
        return time;
    }

    public void setTime(Long time) {
        this.time = time;
    }

    public Integer getCloudCoverMid() {
        return cloudCoverMid;
    }

    public void setCloudCoverMid(Integer cloudCoverMid) {
        this.cloudCoverMid = cloudCoverMid;
    }

    public Integer getCloudCoverLow() {
        return cloudCoverLow;
    }

    public void setCloudCoverLow(Integer cloudCoverLow) {
        this.cloudCoverLow = cloudCoverLow;
    }

    public Integer getCloudCoverHigh() {
        return cloudCoverHigh;
    }

    public void setCloudCoverHigh(Integer cloudCoverHigh) {
        this.cloudCoverHigh = cloudCoverHigh;
    }

    public Integer getCloudCover() {
        return cloudCover;
    }

    public void setCloudCover(Integer cloudCover) {
        this.cloudCover = cloudCover;
    }

    public Float getWindSpeed10m() {
        return windSpeed10m;
    }

    public void setWindSpeed10m(Float windSpeed10m) {
        this.windSpeed10m = windSpeed10m;
    }

    public Integer getWindDirection120m() {
        return windDirection120m;
    }

    public void setWindDirection120m(Integer windDirection120m) {
        this.windDirection120m = windDirection120m;
    }

    public Integer getWeatherCode() {
        return weatherCode;
    }

    public void setWeatherCode(Integer weatherCode) {
        this.weatherCode = weatherCode;
    }

    public Integer getWindDirection80m() {
        return windDirection80m;
    }

    public void setWindDirection80m(Integer windDirection80m) {
        this.windDirection80m = windDirection80m;
    }

    public Integer getWindDirection180m() {
        return windDirection180m;
    }

    public void setWindDirection180m(Integer windDirection180m) {
        this.windDirection180m = windDirection180m;
    }

    public Float getWindSpeed80m() {
        return windSpeed80m;
    }

    public void setWindSpeed80m(Float windSpeed80m) {
        this.windSpeed80m = windSpeed80m;
    }

    public Float getTemperature2m() {
        return temperature2m;
    }

    public void setTemperature2m(Float temperature2m) {
        this.temperature2m = temperature2m;
    }

    public Float getApparentTemperature() {
        return apparentTemperature;
    }

    public void setApparentTemperature(Float apparentTemperature) {
        this.apparentTemperature = apparentTemperature;
    }

    public float getPressureMsl() {
        return pressureMsl;
    }

    public void setPressureMsl(float pressureMsl) {
        this.pressureMsl = pressureMsl;
    }
}
