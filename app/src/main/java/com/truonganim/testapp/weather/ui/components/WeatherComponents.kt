package com.truonganim.testapp.weather.ui.components

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Switch
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.truonganim.testapp.weather.ui.utils.formatHourTime
import com.truonganim.testapp.weather.ui.utils.formatTemperature
import com.truonganim.testapp.weather.ui.utils.formatTime
import com.truonganim.testapp.weather.ui.utils.getWeatherIconText
import com.truonganim.testapp.weather.model.ItemDaily
import com.truonganim.testapp.weather.model.ItemHourly
import com.truonganim.testapp.weather.model.ItemWeather
import com.truonganim.testapp.weather.model.ItemCity
import com.truonganim.testapp.weather.ui.utils.formatDayOfWeek
import com.truonganim.testapp.weather.ui.utils.getWeatherDescription

@Composable
fun CurrentWeatherCard(
    weatherData: ItemWeather?,
    cityData: ItemCity?,
    isShowCTemp: Boolean,
    onTemperatureUnitToggle: () -> Unit
) {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .clip(RoundedCornerShape(16.dp))
            .padding(8.dp)
    ) {
        Column(
            modifier = Modifier.fillMaxWidth(),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Spacer(modifier = Modifier.height(84.dp))
            
            // City name
            Text(
                text = cityData?.getName() ?: "",
                fontSize = 18.sp,
                fontWeight = FontWeight.Normal,
                color = Color.White,
                style = MaterialTheme.typography.headlineMedium
            )
            
            if (weatherData != null) {
                // Data available - show weather information
                val currentTemp = weatherData.getCurrentWeatherTemp()
                val weatherCode = weatherData.getCurrentWeatherCode()
                val today = weatherData.itemDailies().firstOrNull()
                val maxTemp = today?.getTemperature2mMax() ?: 0f
                val minTemp = today?.getTemperature2mMin() ?: 0f
                
                // Current temperature
                Text(
                    text = formatTemperature(currentTemp, isShowCTemp),
                    fontSize = 54.sp,
                    fontWeight = FontWeight.Normal,
                    color = Color.White,
                    style = MaterialTheme.typography.displayLarge
                )
                
                // Weather description
                Text(
                    text = getWeatherDescription(weatherCode),
                    fontSize = 16.sp,
                    color = Color.White.copy(alpha = 0.9f),
                    style = MaterialTheme.typography.bodyLarge
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                // High and Low temperatures
                Row(
                    horizontalArrangement = Arrangement.Center,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "L:${formatTemperature(minTemp, isShowCTemp)}",
                        fontSize = 14.sp,
                        color = Color.White.copy(alpha = 0.9f),
                        style = MaterialTheme.typography.bodyLarge
                    )
                    Text(
                        text = "  |  ",
                        fontSize = 18.sp,
                        color = Color.White.copy(alpha = 0.4f),
                        style = MaterialTheme.typography.bodyLarge
                    )
                    Text(
                        text = "H:${formatTemperature(maxTemp, isShowCTemp)}",
                        fontSize = 14.sp,
                        color = Color.White.copy(alpha = 0.9f),
                        style = MaterialTheme.typography.bodyLarge
                    )
                }
            } else {
                // No data available - show N/A state
                Text(
                    text = "N/A",
                    fontSize = 54.sp,
                    fontWeight = FontWeight.Normal,
                    color = Color.White,
                    style = MaterialTheme.typography.displayLarge
                )
                
                Text(
                    text = "----",
                    fontSize = 16.sp,
                    color = Color.White.copy(alpha = 0.9f),
                    style = MaterialTheme.typography.bodyLarge
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                Row(
                    horizontalArrangement = Arrangement.Center,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "L:--°",
                        fontSize = 14.sp,
                        color = Color.White.copy(alpha = 0.9f),
                        style = MaterialTheme.typography.bodyLarge
                    )
                    Text(
                        text = "  |  ",
                        fontSize = 18.sp,
                        color = Color.White.copy(alpha = 0.4f),
                        style = MaterialTheme.typography.bodyLarge
                    )
                    Text(
                        text = "H:--°",
                        fontSize = 14.sp,
                        color = Color.White.copy(alpha = 0.9f),
                        style = MaterialTheme.typography.bodyLarge
                    )
                }
            }
        }
    }
}

@Composable
fun DetailRow(label: String, value: String) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Text(
            text = label,
            fontSize = 13.sp,
            color = Color.White.copy(alpha = 0.7f)
        )
        Text(
            text = value,
            fontSize = 13.sp,
            color = Color.White.copy(alpha = 0.7f)
        )
    }
}