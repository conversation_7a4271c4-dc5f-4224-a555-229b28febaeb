package com.truonganim.testapp.weather.ui.components

import android.util.Log
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.truonganim.testapp.R
import com.truonganim.testapp.weather.model.ItemHourly
import com.truonganim.testapp.weather.ui.utils.formatHourTime
import com.truonganim.testapp.weather.ui.utils.formatTemperature
import com.truonganim.testapp.weather.ui.utils.getWeatherIcon
import com.truonganim.testapp.weather.ui.utils.getWeatherIconText
import com.truonganim.testapp.weather.ui.utils.timestampToHour
import java.util.*

@Composable
fun HourlyForecastCard(
    hourlyItems: List<ItemHourly>?,
    isShowCTemp: Boolean
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(0.dp),
        shape = RoundedCornerShape(24.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.background.copy(alpha = 0.3f)
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            Text(
                text = "Hourly Forecast",
                fontSize = 16.sp,
                fontWeight = FontWeight.Bold,
                color = Color.White.copy(alpha = 0.4f),
                modifier = Modifier.padding(bottom = 8.dp)
            )

            Spacer(modifier =  Modifier.height(8.dp))

            if (hourlyItems != null && hourlyItems.isNotEmpty()) {
                // Show hourly forecast data
                LazyRow(
                    horizontalArrangement = Arrangement.spacedBy(16.dp)
                ) {
                    items(hourlyItems) { hourly ->
                        HourlyItemCard(hourly = hourly, isShowCTemp = isShowCTemp, isCurrentHour = isCurrentHour(hourly))
                    }
                }
            } else {
                // Show no data state
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(120.dp),
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.Center
                ) {
                    // Cloud error icon
                    Image(
                        painter = painterResource(id = R.drawable.cloud_error),
                        contentDescription = "No data",
                        modifier = Modifier.size(64.dp)
                    )
                    
                    Spacer(modifier = Modifier.height(16.dp))
                    
                    // No information text
                    Text(
                        text = "No information displayed",
                        fontSize = 16.sp,
                        color = Color.White,
                        textAlign = TextAlign.Center
                    )
                }
            }
        }
    }
}

@Composable
fun HourlyItemCard(
    hourly: ItemHourly,
    isShowCTemp: Boolean,
    isCurrentHour: Boolean
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = Modifier.width(50.dp)
    ) {
        Text(
            text = if (isCurrentHour) "Now" else formatHourTime(hourly.getTime() ?: 0),
            fontSize = 14.sp,
            color = Color.White
        )
        
        Spacer(modifier = Modifier.height(8.dp))

        Image(
            painter = painterResource(id = getWeatherIcon(hourly.getWeatherCode() ?: 0, hour = timestampToHour(hourly.getTime()))),
            contentDescription = null,
            modifier = Modifier.size(18.dp)
        )
        
        Spacer(modifier = Modifier.height(8.dp))
        
        Text(
            text = formatTemperature(hourly.getTemperature2m(), isShowCTemp),
            fontSize = 14.sp,
            fontWeight = FontWeight.Medium,
            color = Color.White
        )
    }
}

private fun isCurrentHour(hourly: ItemHourly): Boolean {
    val currentTimeMillis = System.currentTimeMillis() / 1000
    val hourlyTimeMillis = hourly.getTime() ?: 0
    
    // Check if this hourly forecast is the closest to current time
    return hourlyTimeMillis > currentTimeMillis - 3600 && hourlyTimeMillis <= currentTimeMillis + 3600
}

private fun isSameDay(timestamp: Long): Boolean {
    val calendar = Calendar.getInstance()
    val today = calendar.get(Calendar.DAY_OF_YEAR)
    
    calendar.timeInMillis = timestamp * 1000
    val itemDay = calendar.get(Calendar.DAY_OF_YEAR)
    
    return today == itemDay
}

fun getHourlyItemsForToday(hourlyItems: List<ItemHourly>): List<ItemHourly> {
    val currentTimeMillis = System.currentTimeMillis() / 1000
    Log.d("truonghehe", "hourly ${hourlyItems.size}")
    
    // Lọc các dự báo từ thời điểm hiện tại trở đi
    val futureItems = hourlyItems.filter { hourly ->
        val hourlyTime = hourly.getTime() ?: 0
        hourlyTime >= currentTimeMillis
    }
    
    // Lấy tối đa 24 giờ kế tiếp
    return futureItems.take(24)
} 