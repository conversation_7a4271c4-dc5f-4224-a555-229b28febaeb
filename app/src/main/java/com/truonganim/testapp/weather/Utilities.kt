package com.truonganim.testapp.weather

import android.content.Context
import android.content.SharedPreferences
import android.content.pm.PackageManager
import android.os.Build
import android.Manifest
import android.preference.PreferenceManager
import androidx.core.app.ActivityCompat

class Utilities {
    companion object {
        fun locationPermission(context: Context): Boolean {
            return ActivityCompat.checkSelfPermission(context, Manifest.permission.ACCESS_FINE_LOCATION) == PackageManager.PERMISSION_GRANTED ||
                    ActivityCompat.checkSelfPermission(context, Manifest.permission.ACCESS_COARSE_LOCATION) == PackageManager.PERMISSION_GRANTED
        }
        
        fun getPrefs(context: Context): SharedPreferences {
            return context.getSharedPreferences("weather_pref", Context.MODE_PRIVATE)
        }
    }
}