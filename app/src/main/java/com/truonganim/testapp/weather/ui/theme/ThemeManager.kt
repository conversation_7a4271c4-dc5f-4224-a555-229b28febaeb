package com.truonganim.testapp.weather.ui.theme

import android.content.Context
import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.stringPreferencesKey
import com.truonganim.testapp.dataStore
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map

object ThemeManager {
    private val THEME_KEY = stringPreferencesKey("theme_mode")
    
    const val THEME_LIGHT = "light"
    const val THEME_DARK = "dark"
    const val THEME_SYSTEM = "system"
    
    fun getThemeFlow(context: Context): Flow<String> {
        return context.dataStore.data.map { preferences ->
            preferences[THEME_KEY] ?: THEME_SYSTEM
        }
    }
    
    @Composable
    fun shouldUseDarkTheme(context: Context): Boolean {
        val themePreference by getThemeFlow(context).collectAsState(initial = THEME_SYSTEM)
        
        return when (themePreference) {
            THEME_LIGHT -> false
            THEME_DARK -> true
            else -> isSystemInDarkTheme()
        }
    }
    
    suspend fun setTheme(context: Context, theme: String) {
        context.dataStore.edit { preferences ->
            preferences[THEME_KEY] = theme
        }
    }
} 