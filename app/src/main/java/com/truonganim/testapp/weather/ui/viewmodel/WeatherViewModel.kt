package com.truonganim.testapp.weather.ui.viewmodel

import android.Manifest
import android.app.Activity
import android.content.Context
import android.content.pm.PackageManager
import android.location.Location
import android.location.LocationManager
import android.util.Log
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.core.app.ActivityCompat
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.viewModelScope
import com.google.android.gms.location.FusedLocationProviderClient
import com.google.android.gms.location.LocationServices
import com.google.android.gms.location.Priority
import com.google.android.gms.location.CurrentLocationRequest
import com.google.android.gms.tasks.CancellationTokenSource
import com.truonganim.testapp.weather.WeatherRepository
import com.truonganim.testapp.weather.model.ItemCity
import com.truonganim.testapp.weather.model.ItemWeather
import com.truonganim.testapp.weather.ui.utils.logWeatherData
import com.truonganim.testapp.weather.Utilities
import kotlinx.coroutines.launch

private const val TAG = "WeatherViewModel"
private const val KEY_PERMISSION_REQUEST_COUNT = "permission_request_count"
private const val KEY_PERMANENTLY_DENIED = "permanently_denied"

class WeatherViewModel(private val context: Context) : ViewModel() {

    val weatherRepository = WeatherRepository.getInstance(context)
    private val fusedLocationClient: FusedLocationProviderClient =
        LocationServices.getFusedLocationProviderClient(context)
    private val prefs = Utilities.getPrefs(context)
    private val locationManager = context.getSystemService(Context.LOCATION_SERVICE) as LocationManager

    var weatherData by mutableStateOf<ItemWeather?>(weatherRepository.getCachedWeather())
        private set

    var cityData by mutableStateOf<ItemCity?>(weatherRepository.getCachedCity())
        private set

    var isShowCTemp by mutableStateOf(weatherRepository.isShowCTemp())
        private set

    var isLoading by mutableStateOf(false)
        private set

    var hasLocationPermission by mutableStateOf(checkLocationPermission())
        private set

    var isSystemLocationEnabled by mutableStateOf(checkSystemLocationEnabled())
        private set

    var isPermanentlyDenied by mutableStateOf(loadPermanentlyDeniedState())
        private set

    var currentLocation by mutableStateOf<Location?>(null)
        private set

    // Track permission request count to determine if permanently denied
    private var permissionRequestCount = loadPermissionRequestCount()
    private val MAX_PERMISSION_REQUESTS = 2

    init {
        if (hasLocationPermission) {
            if (isSystemLocationEnabled) {
                loadWeatherWithRealLocation()
            }
        }
    }

    // Check if system location services are enabled
    private fun checkSystemLocationEnabled(): Boolean {
        return locationManager.isProviderEnabled(LocationManager.GPS_PROVIDER) || 
               locationManager.isProviderEnabled(LocationManager.NETWORK_PROVIDER)
    }

    // Check system location settings when returning from settings
    fun checkSystemLocationSettings() {
        isSystemLocationEnabled = checkSystemLocationEnabled()
        if (hasLocationPermission && isSystemLocationEnabled) {
            loadWeatherWithRealLocation()
        }
    }

    // Load permission request count from SharedPreferences
    private fun loadPermissionRequestCount(): Int {
        return prefs.getInt(KEY_PERMISSION_REQUEST_COUNT, 0)
    }

    // Save permission request count to SharedPreferences
    private fun savePermissionRequestCount(count: Int) {
        prefs.edit().putInt(KEY_PERMISSION_REQUEST_COUNT, count).apply()
    }

    // Load permanently denied state from SharedPreferences
    private fun loadPermanentlyDeniedState(): Boolean {
        return prefs.getBoolean(KEY_PERMANENTLY_DENIED, false)
    }

    // Save permanently denied state to SharedPreferences
    private fun savePermanentlyDeniedState(denied: Boolean) {
        prefs.edit().putBoolean(KEY_PERMANENTLY_DENIED, denied).apply()
    }

    fun onWeatherDataUpdated(weather: ItemWeather?, city: ItemCity?) {
        weatherData = weather
        cityData = city
        isShowCTemp = weatherRepository.isShowCTemp()
        isLoading = false

        weatherData?.let { logWeatherData(it) }
    }

    fun toggleTemperatureUnit() {
        weatherRepository.setIsShowCTemp(!isShowCTemp)
        isShowCTemp = weatherRepository.isShowCTemp()
    }

    fun loadWeatherWithRealLocation() {
        if (!hasLocationPermission) {
            return
        }

        if (!isSystemLocationEnabled) {
            return
        }

        isLoading = true

        if (ActivityCompat.checkSelfPermission(
                context,
                Manifest.permission.ACCESS_FINE_LOCATION
            ) == PackageManager.PERMISSION_GRANTED || ActivityCompat.checkSelfPermission(
                context,
                Manifest.permission.ACCESS_COARSE_LOCATION
            ) == PackageManager.PERMISSION_GRANTED
        ) {
            viewModelScope.launch {
                try {
                    fusedLocationClient.lastLocation
                        .addOnSuccessListener { location ->
                            if (location != null) {
                                currentLocation = location
                                Log.d(TAG, "Got lastLocation: ${location.latitude}, ${location.longitude}")
                                weatherRepository.loadWeather()
                            } else {
                                Log.d(TAG, "lastLocation is null, fallback to getCurrentLocation")

                                fusedLocationClient.getCurrentLocation(
                                    Priority.PRIORITY_HIGH_ACCURACY,
                                    CancellationTokenSource().token
                                ).addOnSuccessListener { freshLocation ->
                                    if (freshLocation != null) {
                                        currentLocation = freshLocation
                                        Log.d(TAG, "Got current location: ${freshLocation.latitude}, ${freshLocation.longitude}")
                                        weatherRepository.loadWeather()
                                    } else {
                                        Log.d(TAG, "getCurrentLocation is also null")
                                    }
                                    isLoading = false
                                }.addOnFailureListener { e ->
                                    Log.e(TAG, "getCurrentLocation error", e)
                                    isLoading = false
                                }
                            }
                        }
                        .addOnFailureListener { e ->
                            Log.e(TAG, "lastLocation error", e)
                            isLoading = false
                        }
                } catch (e: Exception) {
                    Log.e(TAG, "Exception when getting location", e)
                    isLoading = false
                }
            }
        }
    }

    fun onPermissionResult(granted: Boolean) {
        hasLocationPermission = granted

        if (granted) {
            // Reset permission request count when granted
            permissionRequestCount = 0
            savePermissionRequestCount(0)
            isPermanentlyDenied = false
            savePermanentlyDeniedState(false)
            
            // After app permission is granted, check system location
            if (isSystemLocationEnabled) {
                loadWeatherWithRealLocation()
            }
        } else {
            permissionRequestCount++
            savePermissionRequestCount(permissionRequestCount)

            // After several denials, check if we should show rationale
            val newPermanentlyDenied = checkIfPermanentlyDenied()
            isPermanentlyDenied = newPermanentlyDenied
            savePermanentlyDeniedState(newPermanentlyDenied)

            Log.d(TAG, "Permission denied, isPermanentlyDenied: $isPermanentlyDenied")
            isLoading = false
        }
    }

    private fun checkLocationPermission(): Boolean {
        return ActivityCompat.checkSelfPermission(
            context,
            Manifest.permission.ACCESS_FINE_LOCATION,
        ) == PackageManager.PERMISSION_GRANTED || ActivityCompat.checkSelfPermission(
            context,
            Manifest.permission.ACCESS_COARSE_LOCATION,
        ) == PackageManager.PERMISSION_GRANTED
    }

    private fun checkIfPermanentlyDenied(): Boolean {
        // If permission is granted, it's definitely not permanently denied
        if (checkLocationPermission()) {
            return false
        }

        // If we can show rationale, user hasn't permanently denied
        if (context is Activity) {
            val canShowRationale = ActivityCompat.shouldShowRequestPermissionRationale(
                context,
                Manifest.permission.ACCESS_FINE_LOCATION,
            )

            // If we can't show rationale and we've asked before, it's permanently denied
            return !canShowRationale && permissionRequestCount > 0
        }

        // Fallback to our counter-based approach
        return permissionRequestCount >= MAX_PERMISSION_REQUESTS
    }

    fun refreshWeather() {
        if (hasLocationPermission && isSystemLocationEnabled) {
            loadWeatherWithRealLocation()
        } else {
            Log.d(TAG, "Cannot refresh weather without location permission or system location enabled")
        }
    }

    // Call this when returning from settings to check if permission was granted
    fun checkPermissionAfterSettings() {
        val hasPermissionNow = checkLocationPermission()

        // Update permission state
        hasLocationPermission = hasPermissionNow

        // Update permanent denial state
        val newPermanentlyDenied = if (hasPermissionNow) false else loadPermanentlyDeniedState()
        isPermanentlyDenied = newPermanentlyDenied
        savePermanentlyDeniedState(newPermanentlyDenied)

        // Check system location settings
        isSystemLocationEnabled = checkSystemLocationEnabled()

        if (hasPermissionNow && isSystemLocationEnabled) {
            // Permission was granted in settings and location is enabled
            Log.d(TAG, "Permission granted in settings and location enabled")
            permissionRequestCount = 0
            savePermissionRequestCount(0)
            loadWeatherWithRealLocation()
        } else {
            Log.d(TAG, "Permission still denied or location disabled after settings")
        }
    }

    class Factory(private val context: Context) : ViewModelProvider.Factory {
        @Suppress("UNCHECKED_CAST")
        override fun <T : ViewModel> create(modelClass: Class<T>): T {
            if (modelClass.isAssignableFrom(WeatherViewModel::class.java)) {
                return WeatherViewModel(context) as T
            }
            throw IllegalArgumentException("Unknown ViewModel class")
        }
    }
}
