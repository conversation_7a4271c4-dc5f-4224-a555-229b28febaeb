package com.truonganim.testapp.weather.model;

public class ItemDaily {
    private Long time;
    private Long sunset;
    private Float apparentTemperatureMin;
    private Float apparentTemperatureMax;
    private Float precipitationSum;
    private Float windGusts10mMax;
    private Integer weatherCode;
    private Float temperature2mMax;
    private Float temperature2mMin;
    private Float windDirection10mDominant;
    private Float precipitationHours;
    private Long sunrise;
    private Float windSpeed10mMax;

    public ItemDaily(Long time, Long sunset, Float apparentTemperatureMin,
                     Float apparentTemperatureMax, Float precipitationSum,
                     Float windGusts10mMax, Integer weatherCode, Float temperature2mMax,
                     Float temperature2mMin, Float windDirection10mDominant,
                     Float precipitationHours, Long sunrise, Float windSpeed10mMax) {
        this.time = time;
        this.sunset = sunset;
        this.apparentTemperatureMin = apparentTemperatureMin;
        this.apparentTemperatureMax = apparentTemperatureMax;
        this.precipitationSum = precipitationSum;
        this.windGusts10mMax = windGusts10mMax;
        this.weatherCode = weatherCode;
        this.temperature2mMax = temperature2mMax;
        this.temperature2mMin = temperature2mMin;
        this.windDirection10mDominant = windDirection10mDominant;
        this.precipitationHours = precipitationHours;
        this.sunrise = sunrise;
        this.windSpeed10mMax = windSpeed10mMax;
    }

    public Long getTime() {
        return time;
    }

    public void setTime(Long time) {
        this.time = time;
    }

    public Long getSunset() {
        return sunset;
    }

    public void setSunset(Long sunset) {
        this.sunset = sunset;
    }

    public Float getApparentTemperatureMin() {
        return apparentTemperatureMin;
    }

    public void setApparentTemperatureMin(Float apparentTemperatureMin) {
        this.apparentTemperatureMin = apparentTemperatureMin;
    }

    public Float getApparentTemperatureMax() {
        return apparentTemperatureMax;
    }

    public void setApparentTemperatureMax(Float apparentTemperatureMax) {
        this.apparentTemperatureMax = apparentTemperatureMax;
    }

    public Float getPrecipitationSum() {
        return precipitationSum;
    }

    public void setPrecipitationSum(Float precipitationSum) {
        this.precipitationSum = precipitationSum;
    }

    public Float getWindGusts10mMax() {
        return windGusts10mMax;
    }

    public void setWindGusts10mMax(Float windGusts10mMax) {
        this.windGusts10mMax = windGusts10mMax;
    }

    public Integer getWeatherCode() {
        return weatherCode;
    }

    public void setWeatherCode(Integer weatherCode) {
        this.weatherCode = weatherCode;
    }

    public Float getTemperature2mMax() {
        return temperature2mMax;
    }

    public void setTemperature2mMax(Float temperature2mMax) {
        this.temperature2mMax = temperature2mMax;
    }

    public Float getTemperature2mMin() {
        return temperature2mMin;
    }

    public void setTemperature2mMin(Float temperature2mMin) {
        this.temperature2mMin = temperature2mMin;
    }

    public Float getWindDirection10mDominant() {
        return windDirection10mDominant;
    }

    public void setWindDirection10mDominant(Float windDirection10mDominant) {
        this.windDirection10mDominant = windDirection10mDominant;
    }

    public Float getPrecipitationHours() {
        return precipitationHours;
    }

    public void setPrecipitationHours(Float precipitationHours) {
        this.precipitationHours = precipitationHours;
    }

    public Long getSunrise() {
        return sunrise;
    }

    public void setSunrise(Long sunrise) {
        this.sunrise = sunrise;
    }

    public Float getWindSpeed10mMax() {
        return windSpeed10mMax;
    }

    public void setWindSpeed10mMax(Float windSpeed10mMax) {
        this.windSpeed10mMax = windSpeed10mMax;
    }
}
