package com.truonganim.testapp.weather.ui.components

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp

@Composable
fun FeelsLikeCard(
    modifier: Modifier = Modifier,
    feelsLikeTemp: String?,
    isShowCTemp: Boolean,
    windSpeed: String?,
    pressure: String?,
    apparentTemperatureMax: String?,
    apparentTemperatureMin: String?
) {
    Card(
        modifier = modifier
            .height(180.dp),
        shape = RoundedCornerShape(24.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.background.copy(alpha = 0.3f)
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp),
            verticalArrangement = Arrangement.SpaceBetween
        ) {
            // Title with icon
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "🌡️ Feels Like",
                    fontSize = 16.sp,
                    color = Color.White
                )
            }
            
            // Main value with high/low temps
            Row(
                verticalAlignment = Alignment.Top,
                modifier = Modifier.fillMaxWidth()
            ) {
                Text(
                    text = feelsLikeTemp.takeUnless { it.isNullOrBlank() } ?: "---",
                    fontSize = 32.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color.White
                )
                
                Spacer(modifier = Modifier.weight(1f))
                
                Column(
                    horizontalAlignment = Alignment.End,
                    verticalArrangement = Arrangement.Top
                ) {
                    Text(
                        text = "H: ${apparentTemperatureMax.takeUnless { it.isNullOrBlank() } ?: "---"}",
                        fontSize = 13.sp,
                        color = Color.White
                    )
                    Text(
                        text = "L: ${apparentTemperatureMin.takeUnless { it.isNullOrBlank() } ?: "---"}",
                        fontSize = 13.sp,
                        color = Color.White
                    )
                }
            }

            Spacer(modifier = Modifier.weight(1f))
            
            // Additional details
            Column(
                verticalArrangement = Arrangement.spacedBy(4.dp)
            ) {
                DetailRow("Wind", windSpeed.takeUnless { it.isNullOrBlank() } ?: "---")
                DetailRow("Pressure", pressure.takeUnless { it.isNullOrBlank() } ?: "---")
            }
        }
    }
}

