package com.truonganim.testapp.weather

import android.Manifest
import android.content.pm.PackageManager
import android.os.Bundle
import android.util.Log
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.key
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.core.app.ActivityCompat
import androidx.core.view.WindowCompat
import androidx.lifecycle.ViewModelProvider
import com.truonganim.testapp.weather.ui.screens.WeatherScreen
import com.truonganim.testapp.weather.ui.theme.AppTheme
import com.truonganim.testapp.weather.ui.theme.ThemeManager
import com.truonganim.testapp.weather.ui.viewmodel.WeatherViewModel

class WeatherActivity : ComponentActivity() {
    companion object {
        private const val TAG = "WeatherActivity"
    }

    private lateinit var viewModel: WeatherViewModel

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()

        viewModel = ViewModelProvider(this, WeatherViewModel.Factory(this))
            .get(WeatherViewModel::class.java)

        // Check location permission
        checkLocationPermission()

        // Set content
        setContent {
            val context = LocalContext.current
            val darkTheme = ThemeManager.shouldUseDarkTheme(context)
            
            // Use key to force recomposition when theme changes
            key(darkTheme) {
                AppTheme(darkTheme = darkTheme) {
                    Surface(
                        modifier = Modifier.fillMaxSize(),
                        color = MaterialTheme.colorScheme.background,
                    ) {
                        WeatherScreen()
                    }
                }
            }
        }
    }

    override fun onResume() {
        super.onResume()
        Log.d(TAG, "onResume: Checking permissions")

        // Check if permissions were granted in settings
        viewModel.checkPermissionAfterSettings()
    }

    private fun checkLocationPermission() {
        if (ActivityCompat.checkSelfPermission(
                this,
                Manifest.permission.ACCESS_FINE_LOCATION,
            ) != PackageManager.PERMISSION_GRANTED && ActivityCompat.checkSelfPermission(
                this,
                Manifest.permission.ACCESS_COARSE_LOCATION,
            ) != PackageManager.PERMISSION_GRANTED
        ) {
            // We don't have permission, but we'll let the UI handle this
            Log.d(TAG, "We don't have permission")

            return
        }

        // We have permission, let the ViewModel know
        Log.d(TAG, "We have permission")
        viewModel.onPermissionResult(true)
    }

    override fun onDestroy() {
        super.onDestroy()
        // Clean up any resources
    }
}
