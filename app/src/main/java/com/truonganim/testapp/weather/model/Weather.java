package com.truonganim.testapp.weather.model;

import com.google.gson.annotations.SerializedName;

public class Weather {
    @SerializedName("description")
    private String description;
    @SerializedName("icon")
    private String icon;
    @SerializedName("id")

    /* renamed from: id */
    private int f144id;
    @SerializedName("main")
    private String main;

    public int getId() {
        return this.f144id;
    }

    public String getMain() {
        return this.main;
    }

    public String getDescription() {
        String des = capitalize(this.description);
        return des;
    }

    public String getIcon() {
        return this.icon;
    }

    public static String capitalize(String str) {
        if (str == null) return "";
        return str.substring(0, 1).toUpperCase() + str.substring(1);
    }
}
