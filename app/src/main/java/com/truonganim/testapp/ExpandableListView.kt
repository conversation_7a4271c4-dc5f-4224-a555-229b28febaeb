package com.truonganim.testapp

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView

class ExpandableListView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : ConstraintLayout(context, attrs, defStyleAttr) {

    private val recyclerView: RecyclerView
    private val adapter: ExpandableAdapter
    
    // Default values
    private var collapsedItemCount = 3
    private var expanded = false
    private var items = emptyList<ListItem>()
    
    init {
        // Inflate the layout
        LayoutInflater.from(context).inflate(R.layout.view_expandable_list, this, true)
        
        // Find the RecyclerView
        recyclerView = findViewById(R.id.recyclerView)
        
        // Setup RecyclerView
        adapter = ExpandableAdapter()
        recyclerView.layoutManager = LinearLayoutManager(context)
        recyclerView.adapter = adapter
    }
    
    // Method to set the data
    fun setItems(items: List<ListItem>) {
        this.items = items
        updateAdapterItems()
    }
    
    // Method to set the collapsed item count
    fun setCollapsedItemCount(count: Int) {
        this.collapsedItemCount = count
        if (!expanded) {
            updateAdapterItems()
        }
    }
    
    private fun updateAdapterItems() {
        val displayItems = if (expanded) items else items.take(collapsedItemCount)
        adapter.updateItems(displayItems, expanded)
    }
    
    // Inner adapter class
    private inner class ExpandableAdapter : RecyclerView.Adapter<RecyclerView.ViewHolder>() {
        private var displayItems = emptyList<ListItem>()
        private var isExpanded = false
        
        // View types
        private val VIEW_TYPE_ITEM = 0
        private val VIEW_TYPE_SHOW_MORE = 1
        
        fun updateItems(items: List<ListItem>, expanded: Boolean) {
            this.displayItems = items
            this.isExpanded = expanded
            notifyDataSetChanged()
        }
        
        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
            return when (viewType) {
                VIEW_TYPE_ITEM -> {
                    val view = LayoutInflater.from(parent.context)
                        .inflate(R.layout.item_list_with_icon, parent, false)
                    ItemViewHolder(view)
                }
                VIEW_TYPE_SHOW_MORE -> {
                    val view = LayoutInflater.from(parent.context)
                        .inflate(R.layout.item_show_more, parent, false)
                    ShowMoreViewHolder(view)
                }
                else -> throw IllegalArgumentException("Invalid view type")
            }
        }
        
        override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
            when (holder) {
                is ItemViewHolder -> {
                    val item = displayItems[position]
                    holder.bind(item)
                }
                is ShowMoreViewHolder -> {
                    holder.bind(isExpanded)
                }
            }
        }
        
        override fun getItemCount(): Int {
            return if (isExpanded) {
                displayItems.size + 1 // +1 for "Show less"
            } else {
                displayItems.size + 1 // +1 for "Show more"
            }
        }
        
        override fun getItemViewType(position: Int): Int {
            return if (position < displayItems.size) {
                VIEW_TYPE_ITEM
            } else {
                VIEW_TYPE_SHOW_MORE
            }
        }
        
        // ViewHolder for list items
        inner class ItemViewHolder(view: View) : RecyclerView.ViewHolder(view) {
            private val icon: ImageView = view.findViewById(R.id.imageViewIcon)
            private val title: TextView = view.findViewById(R.id.textViewTitle)
            private val arrow: ImageView = view.findViewById(R.id.imageViewArrow)
            
            fun bind(item: ListItem) {
                icon.setImageResource(item.iconResId)
                title.text = item.title
                
                // Set arrow visibility
                arrow.visibility = if (item.hasArrow) View.VISIBLE else View.GONE
                
                // Set click listener if needed
                itemView.setOnClickListener {
                    // Handle item click if needed
                }
            }
        }
        
        // ViewHolder for show more/less
        inner class ShowMoreViewHolder(view: View) : RecyclerView.ViewHolder(view) {
            private val textView: TextView = view.findViewById(R.id.textViewShowMore)
            
            fun bind(isExpanded: Boolean) {
                textView.text = if (isExpanded) "Show less" else "Show more"
                
                itemView.setOnClickListener {
                    <EMAIL> = !<EMAIL>
                    updateAdapterItems()
                }
            }
        }
    }
    
    // Data class for list items
    data class ListItem(
        val title: String,
        val iconResId: Int,
        val hasArrow: Boolean = true
    )
} 