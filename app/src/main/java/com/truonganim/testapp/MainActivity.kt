package com.truonganim.testapp

import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.widget.TextView
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Button
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.key
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import com.truonganim.testapp.R
import com.truonganim.testapp.ui.theme.TestAppTheme
import com.truonganim.testapp.weather.WeatherActivity
import com.truonganim.testapp.weather.ui.theme.AppTheme
import com.truonganim.testapp.weather.ui.theme.ThemeManager
import com.truonganim.testapp.weather.ui.theme.ThemeSelectionActivity

class MainActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        setContent {
            val context = LocalContext.current
            val darkTheme = ThemeManager.shouldUseDarkTheme(context)
            
            // Use key to force recomposition when theme changes
            key(darkTheme) {
                AppTheme(darkTheme = darkTheme) {
                    Scaffold(modifier = Modifier.fillMaxSize()) { innerPadding ->
                        MainScreen(
                            modifier = Modifier.padding(innerPadding),
                            onWeatherButtonClick = {
                                startActivity(Intent(this, WeatherActivity::class.java))
                            },
                            onThemeButtonClick = {
                                startActivity(Intent(this, ThemeSelectionActivity::class.java))
                            }
                        )
                    }
                }
            }
        }
    }
}

@Composable
fun MainScreen(
    modifier: Modifier = Modifier,
    onWeatherButtonClick: () -> Unit = {},
    onThemeButtonClick: () -> Unit = {}
) {
    val context = LocalContext.current
    
    Column(
        modifier = modifier.fillMaxSize(),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Text(text = "Weather App Demo")
        
        // Custom XML layout with AndroidView
        CustomBoxWithShadowedText(
            text = "Welcome to Weather App!",
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        )
        
        Button(
            onClick = onWeatherButtonClick,
            modifier = Modifier.padding(top = 16.dp)
        ) {
            Text("Show Weather")
        }
        
        Spacer(modifier = Modifier.height(16.dp))
        
        Button(
            onClick = onThemeButtonClick
        ) {
            Text("Change Theme")
        }
    }
}

@Composable
fun CustomBoxWithShadowedText(
    text: String,
    modifier: Modifier = Modifier
) {
    AndroidView(
        modifier = modifier,
        factory = { context ->
            // Inflate the XML layout
            val view = LayoutInflater.from(context).inflate(R.layout.custom_box_layout, null)
            
            // Find the TextView and set its text
            val textView = view.findViewById<TextView>(R.id.shadowTextView)
            textView.text = text
            
            view
        },
        update = { view ->
            // Update the view if needed
            val textView = view.findViewById<TextView>(R.id.shadowTextView)
            textView.text = text
        }
    )
}

@Preview(showBackground = true)
@Composable
fun MainScreenPreview() {
    TestAppTheme {
        MainScreen()
    }
} 