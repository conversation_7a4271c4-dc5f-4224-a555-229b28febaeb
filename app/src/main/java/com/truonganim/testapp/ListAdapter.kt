package com.truonganim.testapp

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView

class ListAdapter(
    private val fullItemList: List<String>,
    private val onShowMoreClick: () -> Unit
) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {
    
    private var isExpanded = false
    private val collapsedSize = 3
    
    // View types
    private val VIEW_TYPE_ITEM = 0
    private val VIEW_TYPE_SHOW_MORE = 1
    
    // The list that will be displayed based on expanded state
    private var displayedItems: List<String> = fullItemList.take(collapsedSize)
    
    class ItemViewHolder(view: View) : RecyclerView.ViewHolder(view) {
        val textView: TextView = view.findViewById(R.id.textViewItem)
    }
    
    class ShowMoreViewHolder(view: View, private val onShowMoreClick: () -> Unit) : RecyclerView.ViewHolder(view) {
        val textView: TextView = view.findViewById(R.id.textViewShowMore)
        
        init {
            view.setOnClickListener {
                onShowMoreClick()
            }
        }
    }
    
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return when (viewType) {
            VIEW_TYPE_ITEM -> {
                val view = LayoutInflater.from(parent.context)
                    .inflate(R.layout.item_list, parent, false)
                ItemViewHolder(view)
            }
            VIEW_TYPE_SHOW_MORE -> {
                val view = LayoutInflater.from(parent.context)
                    .inflate(R.layout.item_show_more, parent, false)
                ShowMoreViewHolder(view, onShowMoreClick)
            }
            else -> throw IllegalArgumentException("Invalid view type")
        }
    }
    
    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        when (holder) {
            is ItemViewHolder -> {
                holder.textView.text = displayedItems[position]
            }
            is ShowMoreViewHolder -> {
                holder.textView.text = if (isExpanded) "Show less" else "Show more"
            }
        }
    }
    
    override fun getItemCount(): Int {
        return if (isExpanded) {
            displayedItems.size
        } else {
            displayedItems.size + 1 // +1 for the "Show more" item
        }
    }
    
    override fun getItemViewType(position: Int): Int {
        return if (!isExpanded && position == displayedItems.size) {
            VIEW_TYPE_SHOW_MORE
        } else {
            VIEW_TYPE_ITEM
        }
    }
    
    fun toggleExpanded() {
        isExpanded = !isExpanded
        displayedItems = if (isExpanded) {
            fullItemList
        } else {
            fullItemList.take(collapsedSize)
        }
        notifyDataSetChanged()
    }
    
    fun isListExpanded(): Boolean {
        return isExpanded
    }
} 