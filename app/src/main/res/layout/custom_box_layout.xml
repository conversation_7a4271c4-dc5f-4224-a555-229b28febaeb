<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="16dp"
    android:background="@drawable/bordered_box_background"
    android:elevation="4dp"
    android:padding="16dp">

    <TextView
        android:id="@+id/shadowTextView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:gravity="center"
        android:padding="16dp"
        android:text="Text with Shadow"
        android:textColor="#FFFFFFFF"
        android:textSize="18sp"
        android:textStyle="bold"
        android:shadowColor="#FF000000"
        android:shadowDx="2"
        android:shadowDy="2"
        android:shadowRadius="4" />

</FrameLayout> 